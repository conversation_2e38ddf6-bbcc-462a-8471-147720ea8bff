/**
 * API Service
 * ===========
 * 
 * Centralized service for making HTTP requests to the backend API.
 * All axios calls should go through this service to maintain consistency
 * and enable easy mocking for tests.
 */

import axios from 'axios'

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for logging and error handling
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

/**
 * API service object containing all API methods
 */
const apiService = {
  /**
   * Get the current status of the recoater system
   * @returns {Promise} Axios response promise
   */
  getStatus() {
    return apiClient.get('/status/')
  },

  /**
   * Perform a health check
   * @returns {Promise} Axios response promise
   */
  getHealth() {
    return apiClient.get('/status/health')
  },

  /**
   * Restart the recoater server
   * @returns {Promise} Axios response promise
   */
  restartServer() {
    return apiClient.post('/status/state?action=restart')
  },

  /**
   * Shutdown the recoater server
   * @returns {Promise} Axios response promise
   */
  shutdownServer() {
    return apiClient.post('/status/state?action=shutdown')
  },

  /**
   * Get recoater configuration
   * @returns {Promise} Axios response promise
   */
  getConfig() {
    return apiClient.get('/config')
  },

  /**
   * Set recoater configuration
   * @param {Object} config - Configuration object
   * @returns {Promise} Axios response promise
   */
  setConfig(config) {
    return apiClient.put('/config', config)
  },

  /**
   * Get drums information
   * @returns {Promise} Axios response promise
   */
  getDrums() {
    return apiClient.get('/drums')
  },

  /**
   * Get specific drum information
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrum(drumId) {
    return apiClient.get(`/drums/${drumId}`)
  },

  // Axis Control API Methods

  /**
   * Get axis status
   * @param {string} axis - Axis identifier ('x', 'z', or 'gripper')
   * @returns {Promise} Axios response promise
   */
  getAxisStatus(axis) {
    return apiClient.get(`/axis/${axis}`)
  },

  /**
   * Move an axis
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @param {Object} motionData - Motion parameters {distance, speed, mode}
   * @returns {Promise} Axios response promise
   */
  moveAxis(axis, motionData) {
    return apiClient.post(`/axis/${axis}/motion`, motionData)
  },

  /**
   * Home an axis
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @param {Object} homingData - Homing parameters {speed}
   * @returns {Promise} Axios response promise
   */
  homeAxis(axis, homingData) {
    return apiClient.post(`/axis/${axis}/home`, homingData)
  },

  /**
   * Get axis motion status
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @returns {Promise} Axios response promise
   */
  getAxisMotion(axis) {
    return apiClient.get(`/axis/${axis}/motion`)
  },

  /**
   * Cancel axis motion
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @returns {Promise} Axios response promise
   */
  cancelAxisMotion(axis) {
    return apiClient.delete(`/axis/${axis}/motion`)
  },

  /**
   * Set gripper state
   * @param {Object} gripperData - Gripper state {enabled}
   * @returns {Promise} Axios response promise
   */
  setGripperState(gripperData) {
    return apiClient.put('/axis/gripper/state', gripperData)
  },

  /**
   * Get gripper state
   * @returns {Promise} Axios response promise
   */
  getGripperState() {
    return apiClient.get('/axis/gripper/state')
  },

  // Drum Control API Methods

  /**
   * Get drum motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Set drum motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, speed, distance, turns)
   * @returns {Promise} Axios response promise
   */
  setDrumMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/motion`, motionData)
  },

  /**
   * Cancel drum motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelDrumMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Get drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {string} unit - Pressure unit ('pascal' or 'bar')
   * @returns {Promise} Axios response promise
   */
  getDrumEjection(drumId, unit = 'pascal') {
    return apiClient.get(`/recoater/drums/${drumId}/ejection`, { params: { unit } })
  },

  /**
   * Set drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {Object} ejectionData - Ejection parameters (target, unit)
   * @returns {Promise} Axios response promise
   */
  setDrumEjection(drumId, ejectionData) {
    return apiClient.put(`/recoater/drums/${drumId}/ejection`, ejectionData)
  },

  /**
   * Get drum suction pressure
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumSuction(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/suction`)
  },

  /**
   * Set drum suction pressure
   * @param {number} drumId - Drum ID
   * @param {Object} suctionData - Suction parameters (target)
   * @returns {Promise} Axios response promise
   */
  setDrumSuction(drumId, suctionData) {
    return apiClient.put(`/recoater/drums/${drumId}/suction`, suctionData)
  },

  // Blade Control API Methods

  /**
   * Get blade screws info
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsInfo(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws`)
  },

  /**
   * Get blade screws motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Set blade screws motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewsMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/motion`, motionData)
  },

  /**
   * Cancel blade screws motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewsMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Get individual blade screw info
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrew(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}`)
  },

  /**
   * Get individual blade screw motion status
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewMotion(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  /**
   * Set individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @param {Object} motionData - Motion parameters (distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewMotion(drumId, screwId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`, motionData)
  },

  /**
   * Cancel individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewMotion(drumId, screwId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  // Leveler Control API Methods

  /**
   * Get leveler pressure information
   * @returns {Promise} Axios response promise
   */
  getLevelerPressure() {
    return apiClient.get('/recoater/leveler/pressure')
  },

  /**
   * Set leveler pressure target
   * @param {number} target - Target pressure in Pa
   * @returns {Promise} Axios response promise
   */
  setLevelerPressure(target) {
    return apiClient.put('/recoater/leveler/pressure', { target })
  },

  /**
   * Get leveler sensor state
   * @returns {Promise} Axios response promise
   */
  getLevelerSensor() {
    return apiClient.get('/recoater/leveler/sensor')
  },

  // Print Control API Methods

  /**
   * Get layer parameters
   * @returns {Promise} Axios response promise
   */
  getLayerParameters() {
    return apiClient.get('/print/layer/parameters')
  },

  /**
   * Set layer parameters
   * @param {Object} parameters - Layer parameters (filling_id, speed, powder_saving, x_offset)
   * @returns {Promise} Axios response promise
   */
  setLayerParameters(parameters) {
    return apiClient.put('/print/layer/parameters', parameters)
  },

  /**
   * Get layer preview image
   * @returns {Promise} Axios response promise with image data
   */
  getLayerPreview() {
    return apiClient.get('/print/layer/preview', {
      responseType: 'blob'
    })
  },

  /**
   * Get drum geometry preview image
   * @param {number} drumId - The ID of the drum to get preview from
   * @returns {Promise} Axios response promise with image data
   */
  getDrumGeometryPreview(drumId) {
    return apiClient.get(`/print/drums/${drumId}/geometry/preview`, {
      responseType: 'blob'
    })
  },

  /**
   * Start print job
   * @returns {Promise} Axios response promise
   */
  startPrintJob() {
    return apiClient.post('/print/job')
  },

  /**
   * Cancel print job
   * @returns {Promise} Axios response promise
   */
  cancelPrintJob() {
    return apiClient.delete('/print/job')
  },

  /**
   * Get print job status
   * @returns {Promise} Axios response promise
   */
  getPrintJobStatus() {
    return apiClient.get('/print/job/status')
  },

  // File Management Methods

  /**
   * Upload geometry file to a specific drum
   * @param {number} drumId - The ID of the drum to upload to
   * @param {File} file - The file to upload (PNG or CLI)
   * @returns {Promise} Axios response promise
   */
  uploadDrumGeometry(drumId, file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post(`/print/drums/${drumId}/geometry`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * Download geometry file from a specific drum
   * @param {number} drumId - The ID of the drum to download from
   * @returns {Promise} Axios response promise with blob data
   */
  downloadDrumGeometry(drumId) {
    return apiClient.get(`/print/drums/${drumId}/geometry`, {
      responseType: 'blob'
    })
  },

  /**
   * Delete geometry file from a specific drum
   * @param {number} drumId - The ID of the drum to delete from
   * @returns {Promise} Axios response promise
   */
  deleteDrumGeometry(drumId) {
    return apiClient.delete(`/print/drums/${drumId}/geometry`)
  },

  // CLI File Management Methods

  /**
   * Upload and parse a multi-layer CLI file
   * @param {File} file - The CLI file to upload
   * @returns {Promise} Axios response promise with file_id and total_layers
   */
  uploadCliFile(file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post('/print/cli/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000
    })
  },

  /**
   * Get a preview of a specific layer from a parsed CLI file
   * @param {string} fileId - The unique ID of the uploaded CLI file
   * @param {number} layerNum - The layer number to preview (1-based)
   * @returns {Promise} Axios response promise with image blob data
   */
  getCliLayerPreview(fileId, layerNum) {
    return apiClient.get(`/print/cli/${fileId}/layer/${layerNum}/preview`, {
      responseType: 'blob'
    })
  },

  /**
   * Send a specific layer from a parsed CLI file to a drum for printing
   * @param {string} fileId - The unique ID of the uploaded CLI file
   * @param {number} layerNum - The layer number to send (1-based)
   * @param {number} drumId - The drum's ID (0-2) to send the layer to
   * @returns {Promise} Axios response promise with operation result
   */
  sendCliLayerToDrum(fileId, layerNum, drumId) {
    return apiClient.post(`/print/cli/${fileId}/layer/${layerNum}/send/${drumId}`)
  },

  /**
   * Send a range of layers from a parsed CLI file to a drum for printing.
   * @param {string} fileId - The unique ID of the uploaded CLI file.
   * @param {number} drumId - The drum's ID (0-2) to send the layer range to.
   * @param {number} startLayer - The starting layer number (1-indexed).
   * @param {number} endLayer - The ending layer number (1-indexed).
   * @returns {Promise} Axios response promise with operation result.
   */
  sendCliLayerRangeToDrum(fileId, drumId, startLayer, endLayer) {
    return apiClient.post(`/print/cli/${fileId}/layers/send/${drumId}`, {
      start_layer: startLayer,
      end_layer: endLayer
    });
  },

  // Configuration API Methods

  /**
   * Get the current recoater configuration
   * @returns {Promise} Axios response promise with configuration data
   */
  async getConfiguration() {
    const response = await apiClient.get('/config/')
    return response.data
  },

  /**
   * Set the recoater configuration
   * @param {Object} config - Configuration object
   * @returns {Promise} Axios response promise
   */
  async setConfiguration(config) {
    const response = await apiClient.put('/config/', config)
    return response.data
  },

  // Multi-Material Job API Methods

  /**
   * Start a multi-material print job with 3 CLI files
   * @param {Object} fileIds - Mapping of drum_id (0,1,2) to file_id
   * @returns {Promise} Axios response promise
   */
  startMultiMaterialJob(fileIds) {
    return apiClient.post('/print/cli/start-multimaterial-job', {
      file_ids: fileIds
    })
  },

  /**
   * Get the current status of the active multi-material job
   * @returns {Promise} Axios response promise
   */
  getMultiMaterialJobStatus() {
    return apiClient.get('/print/multimaterial-job/status')
  },

  /**
   * Cancel the current multi-material job
   * @returns {Promise} Axios response promise
   */
  cancelMultiMaterialJob() {
    return apiClient.post('/print/multimaterial-job/cancel')
  },

  /**
   * Clear backend_error and plc_error flags
   * @returns {Promise} Axios response promise
   */
  clearErrorFlags() {
    return apiClient.post('/print/multimaterial-job/clear-error')
  },

  /**
   * Get status of a specific drum in the multi-material job
   * @param {number} drumId - The drum ID (0, 1, or 2)
   * @returns {Promise} Axios response promise
   */
  getDrumStatus(drumId) {
    return apiClient.get(`/print/multimaterial-job/drum-status/${drumId}`)
  }
}

export { apiService }
export default apiService
