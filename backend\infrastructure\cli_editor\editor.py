"""
CLI Editor Service
==================

This module provides the Editor class, a high-level service for handling CLI (Common Layer Interface)
file operations in the RecoaterHMI backend. It orchestrates parsing, rendering, and generation tasks
by delegating to specialized components (CliFileParser, CliRenderer, CliGenerator).

Key Features:
- Parse CLI files (delegated to CliFileParser).
- Render CLI layers as PNG images for previews.
- Generate new CLI files (binary or ASCII) from layer data.
- Supports drum-specific coloring and layer configuration previews.

Usage Workflow:
1. Parse a CLI file: `editor = Editor(); parsed = editor.parse(cli_bytes)`.
2. Render a layer: `png = editor.render_layer_to_png(layer)`.
3. Generate a new CLI: `new_cli = editor.generate_single_layer_cli(layer)`.

This design promotes modularity, allowing independent testing and extension of parsing, rendering,
and generation functionalities.

Raises:
    CliParsingError: For parsing or generation failures.
    CliRenderingError: For rendering failures.
    CliGenerationError: For generation failures.
"""
import io
import struct
import logging
from typing import List, Optional

from .cli_exceptions import CliParsingError
from .cli_models import CliLayer, ParsedCliFile
from .cli_file_parser import CliFileParser
from .cli_renderer import CliRenderer

# --- Main Service Class ---
class Editor:
    """A service to handle parsing of binary CLI files and rendering of layers."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initializes the parser service."""
        self.logger = logger or logging.getLogger(__name__)
        self.parser = CliFileParser()
        self.renderer = CliRenderer()

    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Parse CLI data using the internal parser"""
        return self.parser.parse(cli_byte_stream)
    
    def _looks_like_ascii(self, data: bytes) -> bool:
        """Delegate to CliFileParser for backward compatibility."""
        return self.parser._looks_like_ascii(data)    

    def render_layer_to_png(self, layer: CliLayer, width: int = 800, height: int = 600, drum_id: int = None) -> bytes:
        """Delegate rendering to CliRenderer."""
        return self.renderer.render_layer_to_png(layer, width, height, drum_id)
    
    def render_layer_configuration_preview(self, width: int = 800, height: int = 600) -> bytes:
        """Delegate layer configuration preview to CliRenderer"""
        return self.renderer.render_layer_configuration_preview(width, height)

    def generate_single_layer_cli(self, layer: CliLayer, header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates CLI file data for a single layer.

        Args:
            layer: The CliLayer object to convert to CLI format
            header_lines: Optional header lines to include (defaults to minimal header)
            is_aligned: Whether to use aligned format (defaults to False)

        Returns:
            A byte string containing the CLI file data for the single layer

        Raises:
            CliParsingError: If the layer data cannot be serialized
        """
        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                header_lines = ["$$HEADEREND"]

            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"

            stream.write(header_str.encode('ascii'))

            # 2. Write Layer Command (127)
            stream.write(struct.pack("<H", 127))  # Layer command code
            if is_aligned:
                stream.write(b'\x00\x00')  # Alignment bytes
            stream.write(struct.pack("<f", layer.z_height))  # Z height

            # 3. Write Polylines (130)
            for poly in layer.polylines:
                if len(poly.points) > 0:
                    stream.write(struct.pack("<H", 130))  # Polyline command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write polyline header
                    stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))

                    # Write points
                    for point in poly.points:
                        stream.write(struct.pack("<f", point.x))
                        stream.write(struct.pack("<f", point.y))

            # 4. Write Hatches (132)
            for hatch in layer.hatches:
                if len(hatch.lines) > 0:
                    stream.write(struct.pack("<H", 132))  # Hatch command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write hatch header
                    stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))

                    # Write hatch lines
                    for line in hatch.lines:
                        stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliParsingError(f"Failed to generate CLI data for layer: {e}")

    def generate_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates a complete binary CLI file from a range of layers.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.
            is_aligned: Whether to use the aligned format.

        Returns:
            A byte string containing the complete CLI file data.

        Raises:
            CliParsingError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliParsingError("Cannot generate CLI data from an empty layer range.")

        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                # Provide a default minimal header if none is given
                header_lines = ["$$HEADEREND"]

            # Ensure the header is correctly terminated
            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"
            
            stream.write(header_str.encode('ascii'))

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command (127)
                stream.write(struct.pack("<H", 127))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<f", layer.z_height))

                # Write Polylines (130)
                for poly in layer.polylines:
                    if len(poly.points) > 0:
                        stream.write(struct.pack("<H", 130))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))
                        for point in poly.points:
                            stream.write(struct.pack("<ff", point.x, point.y))

                # Write Hatches (132)
                for hatch in layer.hatches:
                    if len(hatch.lines) > 0:
                        stream.write(struct.pack("<H", 132))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))
                        for line in hatch.lines:
                            stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliParsingError(f"Failed to generate CLI data for layer range: {e}")

    def generate_single_layer_ascii_cli(self, layer: CliLayer, header_lines: List[str] = None) -> bytes:
        """
        Generates ASCII CLI file data for a single layer.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layer: The CliLayer object to convert to ASCII CLI format
            header_lines: Optional header lines to include (defaults to minimal header)

        Returns:
            A byte string containing the ASCII CLI file data for the single layer

        Raises:
            CliParsingError: If the layer data cannot be serialized
        """
        try:
            lines = []

            # 1. Write Header
            if header_lines is None:
                # Use a minimal header similar to the example file
                header_lines = [
                    "$$HEADERSTART",
                    "$$ASCII",
                    "$$UNITS/00000000.005000",
                    "$$VERSION/200",
                    "$$LABEL/1,default",
                    "$$DATE/060623",
                    "$$DIMENSION/-0000020.000000,-0000020.000000,00000000.080000,00000020.000000,00000020.000000,00000040.000000",
                    "$$LAYERS/000001",
                    "$$HEADEREND"
                ]

            # Add header lines, but update the layer count for single layer
            for line in header_lines:
                if line.startswith("$$LAYERS/"):
                    # Update layer count to 1 for single layer
                    lines.append("$$LAYERS/000001")
                else:
                    lines.append(line)

            # Ensure we have $$GEOMETRYSTART after header
            if "$$GEOMETRYSTART" not in lines:
                lines.append("$$GEOMETRYSTART")

            # 2. Write Layer Command
            lines.append(f"$$LAYER/{layer.z_height}")

            # 3. Write Polylines
            for poly in layer.polylines:
                if len(poly.points) > 0:
                    # Format: $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...
                    point_coords = []
                    for point in poly.points:
                        point_coords.extend([f"{point.x:.5f}", f"{point.y:.5f}"])

                    polyline_data = f"$$POLYLINE/{poly.part_id},{poly.direction},{len(poly.points)},{','.join(point_coords)}"
                    lines.append(polyline_data)

            # 4. Write Hatches
            for hatch in layer.hatches:
                if len(hatch.lines) > 0:
                    # Format: $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...
                    line_coords = []
                    for line in hatch.lines:
                        line_coords.extend([
                            f"{line[0].x:.5f}", f"{line[0].y:.5f}",
                            f"{line[1].x:.5f}", f"{line[1].y:.5f}"
                        ])

                    hatch_data = f"$$HATCHES/{hatch.group_id},{len(hatch.lines)},{','.join(line_coords)}"
                    lines.append(hatch_data)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliParsingError(f"Failed to generate ASCII CLI data for layer: {e}")

    def generate_ascii_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates a complete ASCII CLI file from a range of layers.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.
            is_aligned: Whether the CLI data should be aligned format. Currently not used but kept for compatibility.

        Returns:
            A byte string containing the complete ASCII CLI file data.

        Raises:
            CliParsingError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliParsingError("Cannot generate CLI data from an empty layer range")

        try:
            lines = []

            # 1. Write Header
            if header_lines is None:
                # Use a minimal header similar to the example file
                header_lines = [
                    "$$HEADERSTART",
                    "$$ASCII",
                    "$$UNITS/00000000.005000",
                    "$$VERSION/200",
                    "$$LABEL/1,default",
                    "$$DATE/060623",
                    "$$DIMENSION/-0000020.000000,-0000020.000000,00000000.080000,00000020.000000,00000020.000000,00000040.000000",
                    f"$$LAYERS/{len(layers):06d}",
                    "$$HEADEREND"
                ]

            # Add header lines, but update the layer count
            for line in header_lines:
                if line.startswith("$$LAYERS/"):
                    # Update layer count to match the actual number of layers
                    lines.append(f"$$LAYERS/{len(layers):06d}")
                else:
                    lines.append(line)

            # Ensure we have $$GEOMETRYSTART after header
            if "$$GEOMETRYSTART" not in lines:
                lines.append("$$GEOMETRYSTART")

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command
                lines.append(f"$$LAYER/{layer.z_height}")

                # Write Polylines
                for poly in layer.polylines:
                    if len(poly.points) > 0:
                        # Format: $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...
                        point_coords = []
                        for point in poly.points:
                            point_coords.extend([f"{point.x:.5f}", f"{point.y:.5f}"])

                        polyline_data = f"$$POLYLINE/{poly.part_id},{poly.direction},{len(poly.points)},{','.join(point_coords)}"
                        lines.append(polyline_data)

                # Write Hatches
                for hatch in layer.hatches:
                    if len(hatch.lines) > 0:
                        # Format: $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...
                        line_coords = []
                        for line in hatch.lines:
                            line_coords.extend([
                                f"{line[0].x:.5f}", f"{line[0].y:.5f}",
                                f"{line[1].x:.5f}", f"{line[1].y:.5f}"
                            ])

                        hatch_data = f"$$HATCHES/{hatch.group_id},{len(hatch.lines)},{','.join(line_coords)}"
                        lines.append(hatch_data)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliParsingError(f"Failed to generate ASCII CLI data for layer range: {e}")
