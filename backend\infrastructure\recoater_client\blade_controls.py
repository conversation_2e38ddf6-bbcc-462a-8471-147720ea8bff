"""
Blade Control Methods
====================

This module contains blade control methods for the RecoaterClient.
These methods implement the blade control endpoints from the openapi.json specification.
"""

from typing import Dict, Any


class BladeControlMixin:
    """Mixin class providing blade control methods for RecoaterClient."""

    def get_blade_screws_info(self, drum_id: int) -> Dict[str, Any]:
        """
        Get information about the two screws of the specified drum's scraping blade.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing blade screws information
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws")

    def get_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for the blade screws.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing current motion information, or empty if no motion
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/motion")

    def set_blade_screws_motion(self, drum_id: int, mode: str, distance: float = None) -> Dict[str, Any]:
        """
        Create a motion command for the blade screws.

        Args:
            drum_id: The drum's ID
            mode: Motion mode ('absolute', 'relative', 'homing')
            distance: The distance of the motion [µm] (for absolute/relative modes)

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode
        }

        if distance is not None:
            payload["distance"] = distance

        return self._make_request("POST", f"/drums/{drum_id}/blade/screws/motion", json=payload)

    def cancel_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for the blade screws.

        Args:
            drum_id: The drum's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/blade/screws/motion")

    def get_blade_screw_info(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Get information about a specific screw of the drum's scraping blade.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Dictionary containing blade screw information
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/{screw_id}")

    def get_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Dictionary containing current motion information, or empty if no motion
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/{screw_id}/motion")

    def set_blade_screw_motion(self, drum_id: int, screw_id: int, distance: float) -> Dict[str, Any]:
        """
        Create a motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID
            distance: The relative distance of the motion [µm]

        Returns:
            Response from the API
        """
        payload = {
            "distance": distance
        }
        return self._make_request("POST", f"/drums/{drum_id}/blade/screws/{screw_id}/motion", json=payload)

    def cancel_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/blade/screws/{screw_id}/motion")
