"""
Tests for CLI File Upload Scenarios
===================================

This module contains comprehensive tests for different CLI file upload scenarios
including single drum uploads, dual material printing, triple material printing,
layer range selections, and empty layer template handling.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from io import BytesIO, StringIO

from app.main import app
from app.dependencies import get_recoater_client, get_multilayer_job_manager
from app.services.multilayer_job_manager import MultiMaterialJobManager, MultiMaterialJobError
from backend.infrastructure.cli_editor.editor import Editor, ParsedCliFile, CliLayer, Point, Polyline, Hatch
from infrastructure.recoater_client import RecoaterConnectionError, RecoaterAPIError


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client."""
    client = Mock()
    client.upload_drum_geometry = Mock(return_value={"success": True})
    client.get_drum_status = Mock(return_value={"status": "ready"})
    return client


@pytest.fixture
def mock_multilayer_job_manager():
    """Create a mock multilayer job manager."""
    manager = Mock(spec=MultiMaterialJobManager)
    manager.cli_cache = {}
    manager.current_job = None
    # Return a mock job object with a stable job_id so API response validation passes
    manager.create_job = AsyncMock(return_value=Mock(job_id="test_job_123"))
    # start_job should return True to indicate job start success
    manager.start_job = AsyncMock(return_value=True)
    manager._load_empty_layer_template = AsyncMock()
    manager.cli_parser = Mock()
    manager.cli_parser.parse_cli_file = Mock()
    return manager


@pytest.fixture
def client(mock_recoater_client, mock_multilayer_job_manager):
    """Create a test client for the FastAPI app with mocked dependencies."""
    # Override the dependencies
    app.dependency_overrides[get_recoater_client] = lambda: mock_recoater_client
    app.dependency_overrides[get_multilayer_job_manager] = lambda: mock_multilayer_job_manager
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def sample_cli_content():
    """Sample CLI file content for testing."""
    return """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,test_part
$$LAYERS/000003
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$POLYLINE/1,0,0,0,0
0.0,0.0
10.0,0.0
10.0,10.0
0.0,10.0
0.0,0.0
$$LAYER/0.1
$$POLYLINE/1,0,0,0,0
1.0,1.0
9.0,1.0
9.0,9.0
1.0,9.0
1.0,1.0
$$LAYER/0.2
$$POLYLINE/1,0,0,0,0
2.0,2.0
8.0,2.0
8.0,8.0
2.0,8.0
2.0,2.0
$$HEADEREND"""


@pytest.fixture
def empty_cli_content():
    """Empty CLI file content for testing."""
    return """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,empty
$$LAYERS/000001
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$HEADEREND"""



class TestSingleDrumCLIUploads:
    """Test single drum CLI upload scenarios."""

    def test_single_drum_upload_success(self, client, sample_cli_content):
        """Test successful single drum CLI upload."""
        # Create file-like object
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "test_part.cli"

        # Upload CLI file
        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("test_part.cli", cli_file, "application/octet-stream")}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_layers"] == 3
        assert "file_id" in data

    def test_single_drum_upload_invalid_file(self, client):
        """Test single drum upload with invalid file type."""
        # Create non-CLI file
        invalid_file = BytesIO(b"not a cli file")
        invalid_file.name = "test.txt"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("test.txt", invalid_file, "text/plain")}
        )

        assert response.status_code == 400
        assert "Only .cli files are supported" in response.json()["detail"]

    def test_single_drum_upload_empty_file(self, client):
        """Test single drum upload with empty file."""
        empty_file = BytesIO(b"")
        empty_file.name = "empty.cli"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("empty.cli", empty_file, "application/octet-stream")}
        )

        assert response.status_code == 400
        assert "Uploaded file is empty" in response.json()["detail"]


class TestDualMaterialPrinting:
    """Test dual material printing scenarios (2 drums with files, 1 empty)."""

    @pytest.mark.asyncio
    async def test_dual_material_job_creation(self, sample_cli_content):
        """Test creating a dual material job with 2 files."""
        # Create a real-like mock that will actually call _load_empty_layer_template
        manager = Mock(spec=MultiMaterialJobManager)
        manager.cli_cache = {
            "file1": Mock(layers=[Mock() for _ in range(3)]),
            "file2": Mock(layers=[Mock() for _ in range(3)])
        }
        
        # Mock the method and configure it to be called
        manager._load_empty_layer_template = AsyncMock(return_value="empty_template_123")
        
        # Create a side_effect for create_job that calls _load_empty_layer_template
        async def create_job_side_effect(file_ids):
            if len(file_ids) == 2:  # Dual material, need empty template
                await manager._load_empty_layer_template()
            return Mock(job_id="test_job")
        
        manager.create_job = AsyncMock(side_effect=create_job_side_effect)
        
        # Test dual material job creation
        file_ids = {0: "file1", 1: "file2"}
        await manager.create_job(file_ids)
        
        # Verify empty template was loaded
        manager._load_empty_layer_template.assert_called_once()

    @pytest.mark.asyncio
    async def test_dual_material_validation_error(self):
        """Test dual material job with insufficient files."""
        manager = Mock(spec=MultiMaterialJobManager)
        manager.cli_cache = {"file1": Mock()}
        
        # Configure create_job to raise the expected error
        manager.create_job = AsyncMock(side_effect=MultiMaterialJobError("Must provide 2-3 CLI files"))
        
        file_ids = {0: "file1"}
        
        with pytest.raises(MultiMaterialJobError, match="Must provide 2-3 CLI files"):
            await manager.create_job(file_ids)


class TestTripleMaterialPrinting:
    """Test triple material printing scenarios (3 drums with files)."""

    @pytest.mark.asyncio
    async def test_triple_material_job_creation(self, mock_multilayer_job_manager):
        """Test creating a triple material job with 3 files."""
        # Setup mock parsed files
        mock_parsed_file = Mock(spec=ParsedCliFile)
        mock_parsed_file.layers = [Mock(spec=CliLayer) for _ in range(5)]
        
        mock_multilayer_job_manager.cli_cache = {
            "file1": mock_parsed_file,
            "file2": mock_parsed_file,
            "file3": mock_parsed_file
        }
        
        # Test triple material job creation
        file_ids = {0: "file1", 1: "file2", 2: "file3"}
        
        # This should succeed without needing empty template
        result = await mock_multilayer_job_manager.create_job(file_ids)
        
        # Verify no empty template was needed
        assert not hasattr(mock_multilayer_job_manager, '_load_empty_layer_template') or \
               not mock_multilayer_job_manager._load_empty_layer_template.called

    def test_triple_material_api_endpoint(self, client):
        """Test triple material job via API endpoint."""
        # Mock all the required dependencies
        with patch('app.api.print.get_multilayer_job_manager') as mock_get_manager, \
             patch('app.api.print.cli_file_cache') as mock_cache:
            
            # Setup manager mock
            mock_manager = Mock()
            
            # Create a simple class for the job state
            class MockJobState:
                def __init__(self, job_id):
                    self.job_id = job_id
            
            mock_job = MockJobState("test_job_123")
            
            mock_manager.create_job = AsyncMock(return_value=mock_job)
            mock_manager.start_job = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager
            
            # Setup cache mock to contain the files
            mock_cache.__contains__ = Mock(side_effect=lambda x: x in ["file1", "file2", "file3"])
            
            # Test API call with 3 files
            response = client.post(
                "/api/v1/print/cli/start-multimaterial-job",
                json={
                    "file_ids": {
                        "0": "file1",
                        "1": "file2", 
                        "2": "file3"
                    }
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["job_id"] == "test_job_123"


class TestLayerRangeManagement:
    """Test layer range selections and management."""

    def test_layer_range_validation(self, client):
        """Test layer range validation."""
        # Mock CLI file cache
        with patch('app.api.print.cli_file_cache') as mock_cache, \
             patch('app.api.print.get_recoater_client') as mock_get_client:
            
            # Setup cache mock with proper layer structure
            mock_layer = Mock(spec=CliLayer)
            mock_layer.z_height = 0.1
            mock_layer.polylines = []
            mock_layer.hatches = []
            
            mock_parsed_file = Mock(spec=ParsedCliFile)
            mock_parsed_file.layers = [mock_layer for _ in range(10)]  # 10 layers
            mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
            
            # Cache stores dictionaries with 'parsed_file' and 'original_filename'
            cache_entry = {
                'parsed_file': mock_parsed_file,
                'original_filename': 'test_file.cli'
            }
            
            mock_cache.__contains__ = Mock(return_value=True)
            mock_cache.__getitem__ = Mock(return_value=cache_entry)
            
            # Setup recoater client mock
            mock_client = Mock()
            mock_client.upload_drum_geometry = Mock(return_value={"success": True})
            mock_get_client.return_value = mock_client
            
            # Test valid layer range using the correct endpoint
            response = client.post(
                "/api/v1/print/cli/test_file/layers/send/0",
                json={"start_layer": 3, "end_layer": 7}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["layer_range"] == [3, 7]

    def test_invalid_layer_range(self, client):
        """Test invalid layer range handling."""
        with patch('app.api.print.cli_file_cache') as mock_cache:
            mock_cache.__contains__.return_value = False
            
            response = client.post(
                "/api/v1/print/cli/nonexistent_file/layers/send/0",
                json={"start_layer": 1, "end_layer": 5}
            )
            
            assert response.status_code == 404
            assert "CLI file not found" in response.json()["detail"]


class TestEmptyLayerTemplateHandling:
    """Test empty layer template handling."""

    @pytest.mark.asyncio
    async def test_empty_template_loading(self, empty_cli_content):
        """Test loading empty layer template."""
        manager = Mock(spec=MultiMaterialJobManager)
        manager.cli_cache = {}
        
        # Setup CLI parser mock properly
        mock_parser = Mock()
        mock_parsed_template = Mock(spec=ParsedCliFile)
        mock_parsed_template.layers = [Mock(spec=CliLayer)]
        mock_parser.parse_cli_file = Mock(return_value=mock_parsed_template)
        manager.cli_parser = mock_parser
        
        # Mock the actual method
        async def load_empty_template():
            template_id = "empty_template_123"
            manager.cli_cache[template_id] = mock_parsed_template
            return template_id
        
        manager._load_empty_layer_template = AsyncMock(side_effect=load_empty_template)
        
        template_id = await manager._load_empty_layer_template()
        
        assert template_id == "empty_template_123"
        assert template_id in manager.cli_cache

    @pytest.mark.asyncio
    async def test_empty_template_missing_file(self):
        """Test handling missing empty template file."""
        manager = Mock(spec=MultiMaterialJobManager)
        
        # Configure the method to raise the expected error
        manager._load_empty_layer_template = AsyncMock(
            side_effect=MultiMaterialJobError("Empty layer template not found")
        )
        
        with pytest.raises(MultiMaterialJobError, match="Empty layer template not found"):
            await manager._load_empty_layer_template()


def mock_open(read_data):
    """Helper function to create a mock open context manager."""
    from unittest.mock import mock_open as _mock_open
    return _mock_open(read_data=read_data)


class TestCLIFileIntegration:
    """Test CLI file integration scenarios."""

    def test_cli_upload_and_layer_send_workflow(self, client, sample_cli_content):
        """Test complete workflow: upload CLI file and send specific layer."""
        # Step 1: Upload CLI file
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "workflow_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("workflow_test.cli", cli_file, "application/octet-stream")}
        )

        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Step 2: Mock CLI cache and recoater client for layer upload
        with patch('app.api.print.cli_file_cache') as mock_cache, \
             patch('app.api.print.get_recoater_client') as mock_get_client:
            
            # Setup cache mock with proper layer structure
            mock_layer = Mock(spec=CliLayer)
            mock_layer.z_height = 0.1
            mock_layer.polylines = []
            mock_layer.hatches = []
            
            mock_parsed_file = Mock(spec=ParsedCliFile)
            mock_parsed_file.layers = [mock_layer for _ in range(3)]
            mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
            
            # Cache stores dictionaries with 'parsed_file' and 'original_filename'
            cache_entry = {
                'parsed_file': mock_parsed_file,
                'original_filename': 'workflow_test.cli'
            }
            
            mock_cache.__contains__ = Mock(return_value=True)
            mock_cache.__getitem__ = Mock(return_value=cache_entry)
            
            # Setup recoater client mock
            mock_client = Mock()
            mock_client.upload_drum_geometry.return_value = {"success": True}
            mock_get_client.return_value = mock_client

            # Step 3: Send specific layer to drum
            layer_response = client.post(f"/api/v1/print/cli/{file_id}/layer/2/send/0")

            assert layer_response.status_code == 200
            data = layer_response.json()
            assert data["success"] is True
            assert data["layer_num"] == 2
            assert data["drum_id"] == 0

    def test_cli_upload_and_range_send_workflow(self, client, sample_cli_content):
        """Test complete workflow: upload CLI file and send layer range."""
        # Step 1: Upload CLI file
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "range_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("range_test.cli", cli_file, "application/octet-stream")}
        )

        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Step 2: Mock CLI cache and recoater client for range upload
        with patch('app.api.print.cli_file_cache') as mock_cache, \
             patch('app.api.print.get_recoater_client') as mock_get_client:
            
            # Setup cache mock with proper layer structure
            mock_layer = Mock(spec=CliLayer)
            mock_layer.z_height = 0.1
            mock_layer.polylines = []
            mock_layer.hatches = []
            
            mock_parsed_file = Mock(spec=ParsedCliFile)
            mock_parsed_file.layers = [mock_layer for _ in range(3)]
            mock_parsed_file.header_lines = ["$$HEADERSTART", "$$HEADEREND"]
            
            # Cache stores dictionaries with 'parsed_file' and 'original_filename'
            cache_entry = {
                'parsed_file': mock_parsed_file,
                'original_filename': 'range_test.cli'
            }
            
            mock_cache.__contains__ = Mock(return_value=True)
            mock_cache.__getitem__ = Mock(return_value=cache_entry)
            
            # Setup recoater client mock
            mock_client = Mock()
            mock_client.upload_drum_geometry.return_value = {"success": True}
            mock_get_client.return_value = mock_client

            # Step 3: Send layer range to drum
            range_response = client.post(
                f"/api/v1/print/cli/{file_id}/layers/send/1",
                json={"start_layer": 1, "end_layer": 3}
            )

            assert range_response.status_code == 200
            data = range_response.json()
            assert data["success"] is True
            assert data["layer_range"] == [1, 3]
            assert data["drum_id"] == 1


class TestErrorHandlingScenarios:
    """Test error handling in various CLI scenarios."""

    def test_multimaterial_job_with_invalid_drum_id(self, client):
        """Test multi-material job with invalid drum ID."""
        payload = {"file_ids": {"0": "file1", "3": "file2"}}
        response = client.post("/api/v1/print/cli/start-multimaterial-job", json=payload)
        assert response.status_code == 400
        assert "Invalid drum ID" in response.json()["detail"]

    def test_multimaterial_job_with_too_many_files(self, client):
        """Test multi-material job with too many files."""
        payload = {"file_ids": {"0": "file1", "1": "file2", "2": "file3", "3": "file4"}}
        response = client.post("/api/v1/print/cli/start-multimaterial-job", json=payload)
        assert response.status_code == 400, response.json()
        assert "Must provide 1-3 CLI files" in response.json()["detail"]

    def test_layer_send_with_connection_error(self, client, sample_cli_content, mock_recoater_client):
        """Test layer send with recoater connection error."""
        # Upload CLI file first
        cli_file = BytesIO(sample_cli_content.encode('utf-8'))
        cli_file.name = "error_test.cli"

        upload_response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("error_test.cli", cli_file, "application/octet-stream")}
        )
        
        # Ensure upload was successful first
        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Configure the recoater client to raise a connection error
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        response = client.post(f"/api/v1/print/cli/{file_id}/layer/1/send/0")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]


class TestPerformanceScenarios:
    """Test performance-related CLI scenarios."""

    def test_large_cli_file_handling(self, client):
        """Test handling of large CLI files."""
        # Create a large CLI file content (simulate many layers)
        large_cli_content = """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,large_part
$$LAYERS/000100
$$HEADEREND
$$GEOMETRYSTART"""

        # Add 100 layers
        for i in range(100):
            layer_height = i * 0.1
            large_cli_content += f"""
$$LAYER/{layer_height:.1f}
$$POLYLINE/1,0,0,0,0
{i}.0,{i}.0
{i+10}.0,{i}.0
{i+10}.0,{i+10}.0
{i}.0,{i+10}.0
{i}.0,{i}.0"""

        large_cli_content += "\n$$HEADEREND"

        # Test upload
        cli_file = BytesIO(large_cli_content.encode('utf-8'))
        cli_file.name = "large_part.cli"

        response = client.post(
            "/api/v1/print/cli/upload",
            files={"file": ("large_part.cli", cli_file, "application/octet-stream")}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_layers"] == 100

    def test_concurrent_cli_uploads(self, client, sample_cli_content):
        """Test concurrent CLI file uploads."""
        import threading
        import time

        results = []

        def upload_cli_file(file_suffix):
            cli_file = BytesIO(sample_cli_content.encode('utf-8'))
            cli_file.name = f"concurrent_test_{file_suffix}.cli"

            response = client.post(
                "/api/v1/print/cli/upload",
                files={"file": (f"concurrent_test_{file_suffix}.cli", cli_file, "application/octet-stream")}
            )
            results.append(response.status_code)

        # Create multiple threads for concurrent uploads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=upload_cli_file, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All uploads should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 5
