"""
Tests for Recoater Retry Logic and Heartbeat Mechanism
======================================================

This module contains tests for the retry functionality in RecoaterClient
and the heartbeat mechanism that maintains hardware connections.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
import requests
from requests.exceptions import ConnectionError, Timeout

from infrastructure.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.utils.heartbeat import HeartbeatManager, start_heartbeat_task, stop_heartbeat_task


class TestRecoaterRetryLogic:
    """Test class for RecoaterClient retry functionality."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.base_url = "http://172.16.17.224:8080"
        self.client = RecoaterClient(self.base_url, timeout=5.0)
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_retry_on_connection_error(self, mock_request, mock_sleep):
        """Test that _make_request retries on ConnectionError and succeeds on second attempt."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"state": "ready", "timestamp": "2025-07-09T14:30:00Z"}
        
        # First call raises ConnectionError, second call succeeds
        mock_request.side_effect = [
            ConnectionError("Connection failed"),
            mock_response
        ]
        
        # Act
        result = self.client.get_state()
        
        # Assert
        assert result["state"] == "ready"
        assert result["timestamp"] == "2025-07-09T14:30:00Z"
        assert mock_request.call_count == 2  # Initial attempt + 1 retry
        mock_sleep.assert_called_once_with(0.5)  # 500ms retry delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_retry_on_timeout(self, mock_request, mock_sleep):
        """Test that _make_request retries on Timeout and succeeds on second attempt."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"drums": [{"id": 1, "name": "Drum 1"}]}
        
        # First call raises Timeout, second call succeeds
        mock_request.side_effect = [
            Timeout("Request timed out"),
            mock_response
        ]
        
        # Act
        result = self.client.get_drums()
        
        # Assert
        assert len(result["drums"]) == 1
        assert result["drums"][0]["id"] == 1
        assert mock_request.call_count == 2  # Initial attempt + 1 retry
        mock_sleep.assert_called_once_with(0.5)  # 500ms retry delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_fails_after_all_retries(self, mock_request, mock_sleep):
        """Test that _make_request raises RecoaterConnectionError after all retries are exhausted."""
        # Arrange
        # Both calls raise ConnectionError
        mock_request.side_effect = [
            ConnectionError("Connection failed"),
            ConnectionError("Connection failed again")
        ]
        
        # Act & Assert
        with pytest.raises(RecoaterConnectionError) as exc_info:
            self.client.get_state()
        
        assert "Failed to connect to recoater after 2 attempts" in str(exc_info.value)
        assert mock_request.call_count == 2  # Initial attempt + 1 retry
        mock_sleep.assert_called_once_with(0.5)  # 500ms retry delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_no_retry_on_api_error(self, mock_request, mock_sleep):
        """Test that _make_request does not retry on API errors (HTTP 4xx/5xx)."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_request.return_value = mock_response
        
        # Act & Assert
        with pytest.raises(RecoaterAPIError) as exc_info:
            self.client.get_state()
        
        assert "API returned status 500" in str(exc_info.value)
        assert mock_request.call_count == 1  # No retry on API errors
        mock_sleep.assert_not_called()  # No sleep/retry delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_no_retry_on_other_request_exceptions(self, mock_request, mock_sleep):
        """Test that _make_request does not retry on other RequestException types."""
        # Arrange
        mock_request.side_effect = requests.exceptions.HTTPError("HTTP Error")
        
        # Act & Assert
        with pytest.raises(RecoaterConnectionError) as exc_info:
            self.client.get_state()
        
        assert "Request failed" in str(exc_info.value)
        assert mock_request.call_count == 1  # No retry on other RequestExceptions
        mock_sleep.assert_not_called()  # No sleep/retry delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_retry_timing(self, mock_request, mock_sleep):
        """Test that retry delay is exactly 500ms."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"config": {"test": "value"}}
        
        # First call raises ConnectionError, second call succeeds
        mock_request.side_effect = [
            ConnectionError("Connection failed"),
            mock_response
        ]
        
        # Act
        result = self.client.get_config()
        
        # Assert
        assert result["config"]["test"] == "value"
        mock_sleep.assert_called_once_with(0.5)  # Verify exact 500ms delay
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    @patch('requests.Session.request')
    def test_make_request_retry_preserves_kwargs(self, mock_request, mock_sleep):
        """Test that retry preserves all request kwargs (json, params, etc.)."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        
        config_data = {"build_space_diameter": 120, "resolution": 60}
        
        # First call raises ConnectionError, second call succeeds
        mock_request.side_effect = [
            ConnectionError("Connection failed"),
            mock_response
        ]
        
        # Act
        result = self.client.set_config(config_data)
        
        # Assert
        assert result["status"] == "success"
        assert mock_request.call_count == 2
        
        # Verify both calls had the same arguments
        first_call = mock_request.call_args_list[0]
        second_call = mock_request.call_args_list[1]
        
        assert first_call[1]['json'] == config_data
        assert second_call[1]['json'] == config_data
        assert first_call[1]['method'] == 'PUT'
        assert second_call[1]['method'] == 'PUT'


class TestHeartbeatMechanism:
    """Test class for heartbeat functionality."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        # Ensure no global heartbeat manager exists
        import app.utils.heartbeat
        app.utils.heartbeat._heartbeat_manager = None
    
    def teardown_method(self):
        """Clean up after each test method."""
        # Ensure heartbeat is stopped after each test
        import app.utils.heartbeat
        if app.utils.heartbeat._heartbeat_manager:
            asyncio.run(stop_heartbeat_task())
    
    @pytest.mark.asyncio
    @patch('app.utils.heartbeat.get_recoater_client_instance')
    async def test_heartbeat_calls_get_drums(self, mock_get_client):
        """Test that heartbeat task calls get_drums() periodically."""
        # Arrange
        mock_client = Mock()
        mock_client.get_drums.return_value = {"drums": []}
        mock_get_client.return_value = mock_client
        
        # Create heartbeat manager with short interval for testing
        heartbeat_manager = HeartbeatManager(interval=0.1)  # 100ms for fast testing
        
        # Act
        task = heartbeat_manager.start()
        
        # Wait for at least 2 heartbeat cycles
        await asyncio.sleep(0.25)
        
        # Stop the heartbeat
        await heartbeat_manager.stop()
        
        # Assert
        assert mock_client.get_drums.call_count >= 2  # Should be called at least twice
        assert task.done()  # Task should be completed after stop
    
    @pytest.mark.asyncio
    @patch('app.utils.heartbeat.get_recoater_client_instance')
    async def test_heartbeat_continues_on_connection_error(self, mock_get_client):
        """Test that heartbeat continues running even when get_drums() raises ConnectionError."""
        # Arrange
        mock_client = Mock()
        mock_client.get_drums.side_effect = RecoaterConnectionError("Connection failed")
        mock_get_client.return_value = mock_client
        
        # Create heartbeat manager with short interval for testing
        heartbeat_manager = HeartbeatManager(interval=0.1)  # 100ms for fast testing
        
        # Act
        task = heartbeat_manager.start()
        
        # Wait for several heartbeat cycles
        await asyncio.sleep(0.35)
        
        # Verify heartbeat is still running
        assert heartbeat_manager.is_running
        assert not task.done()
        
        # Stop the heartbeat
        await heartbeat_manager.stop()
        
        # Assert
        assert mock_client.get_drums.call_count >= 3  # Should continue calling despite errors
        assert task.done()  # Task should be completed after stop
    
    @pytest.mark.asyncio
    @patch('app.utils.heartbeat.get_recoater_client_instance')
    async def test_heartbeat_handles_no_client(self, mock_get_client):
        """Test that heartbeat handles gracefully when no client is available."""
        # Arrange
        mock_get_client.return_value = None  # No client available
        
        # Create heartbeat manager with short interval for testing
        heartbeat_manager = HeartbeatManager(interval=0.1)  # 100ms for fast testing
        
        # Act
        task = heartbeat_manager.start()
        
        # Wait for several heartbeat cycles
        await asyncio.sleep(0.25)
        
        # Verify heartbeat is still running despite no client
        assert heartbeat_manager.is_running
        assert not task.done()
        
        # Stop the heartbeat
        await heartbeat_manager.stop()
        
        # Assert
        assert task.done()  # Task should be completed after stop
    
    @pytest.mark.asyncio
    async def test_heartbeat_manager_lifecycle(self):
        """Test HeartbeatManager start/stop lifecycle."""
        # Arrange
        heartbeat_manager = HeartbeatManager(interval=1.0)
        
        # Initially not running
        assert not heartbeat_manager.is_running
        assert heartbeat_manager.task is None
        
        # Act - Start
        task = heartbeat_manager.start()
        
        # Assert - Running
        assert heartbeat_manager.is_running
        assert heartbeat_manager.task is not None
        assert heartbeat_manager.task == task
        assert not task.done()
        
        # Act - Stop
        await heartbeat_manager.stop()
        
        # Assert - Stopped
        assert not heartbeat_manager.is_running
        assert heartbeat_manager.task is None
        assert task.done()
    
    @pytest.mark.asyncio
    async def test_heartbeat_manager_double_start_error(self):
        """Test that starting heartbeat twice raises RuntimeError."""
        # Arrange
        heartbeat_manager = HeartbeatManager(interval=1.0)
        
        # Act - Start first time
        task1 = heartbeat_manager.start()
        
        # Act & Assert - Start second time should raise error
        with pytest.raises(RuntimeError, match="Heartbeat is already running"):
            heartbeat_manager.start()
        
        # Cleanup
        await heartbeat_manager.stop()
    
    @pytest.mark.asyncio
    async def test_heartbeat_manager_stop_when_not_running(self):
        """Test that stopping heartbeat when not running is safe."""
        # Arrange
        heartbeat_manager = HeartbeatManager(interval=1.0)
        
        # Act & Assert - Should not raise error
        await heartbeat_manager.stop()
        
        # Still not running
        assert not heartbeat_manager.is_running
        assert heartbeat_manager.task is None
    
    @pytest.mark.asyncio
    @patch('app.utils.heartbeat.get_recoater_client_instance')
    async def test_global_heartbeat_functions(self, mock_get_client):
        """Test global start_heartbeat_task and stop_heartbeat_task functions."""
        # Arrange
        mock_client = Mock()
        mock_client.get_drums.return_value = {"drums": []}
        mock_get_client.return_value = mock_client
        
        # Act - Start global heartbeat
        task = start_heartbeat_task(interval=0.1)  # 100ms for fast testing
        
        # Wait briefly
        await asyncio.sleep(0.15)
        
        # Assert - Heartbeat is running
        assert not task.done()
        assert mock_client.get_drums.call_count >= 1
        
        # Act - Stop global heartbeat
        await stop_heartbeat_task()
        
        # Assert - Heartbeat is stopped
        assert task.done()
    
    @pytest.mark.asyncio
    async def test_global_heartbeat_double_start_error(self):
        """Test that starting global heartbeat twice raises RuntimeError."""
        # Act - Start first time
        task1 = start_heartbeat_task(interval=1.0)
        
        # Act & Assert - Start second time should raise error
        with pytest.raises(RuntimeError, match="Heartbeat task is already running"):
            start_heartbeat_task(interval=1.0)
        
        # Cleanup
        await stop_heartbeat_task()
    
    @pytest.mark.asyncio
    async def test_global_heartbeat_stop_when_not_running(self):
        """Test that stopping global heartbeat when not running is safe."""
        # Act & Assert - Should not raise error
        await stop_heartbeat_task()
    
    @pytest.mark.asyncio
    @patch('app.utils.heartbeat.get_recoater_client_instance')
    async def test_heartbeat_handles_unexpected_errors(self, mock_get_client):
        """Test that heartbeat continues running even when unexpected errors occur."""
        # Arrange
        mock_client = Mock()
        mock_client.get_drums.side_effect = [
            Exception("Unexpected error"),  # First call raises unexpected error
            {"drums": []},  # Second call succeeds
            Exception("Another error"),  # Third call raises error again
            {"drums": []}   # Fourth call succeeds
        ]
        mock_get_client.return_value = mock_client
        
        # Create heartbeat manager with short interval for testing
        heartbeat_manager = HeartbeatManager(interval=0.1)  # 100ms for fast testing
        
        # Act
        task = heartbeat_manager.start()
        
        # Wait for several heartbeat cycles
        await asyncio.sleep(0.45)
        
        # Verify heartbeat is still running
        assert heartbeat_manager.is_running
        assert not task.done()
        
        # Stop the heartbeat
        await heartbeat_manager.stop()
        
        # Assert
        assert mock_client.get_drums.call_count >= 4  # Should continue calling despite errors
        assert task.done()  # Task should be completed after stop
