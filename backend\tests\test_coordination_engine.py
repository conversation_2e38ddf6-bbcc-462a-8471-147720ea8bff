"""
Tests for Multi-Material Coordination Engine
============================================

Comprehensive tests for Stage 3 coordination logic including:
- Complete job coordination
- Error injection and handling
- Partial failures and recovery
- Timeout scenarios
- 3-drum coordination
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from app.services.coordination_engine import (
    MultiMaterialCoordinationEngine,
    CoordinationState,
    CoordinationError
)
from app.models.multilayer_job import MultiMaterialJobState, JobStatus, DrumState
from backend.infrastructure.cli_editor.editor import Point, <PERSON><PERSON><PERSON>, CliLayer, ParsedCliFile
from infrastructure.mock_recoater_client import MockRecoaterClient
from app.services.multilayer_job_manager import MultiMaterialJobManager


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client for testing."""
    client = MockRecoaterClient("http://mock-recoater:8080")
    client.reset_multimaterial_state()
    return client


@pytest.fixture
def mock_job_manager():
    """Create a mock job manager for testing."""
    manager = Mock(spec=MultiMaterialJobManager)
    return manager


@pytest.fixture
def coordination_engine(mock_recoater_client, mock_job_manager):
    """Create a coordination engine for testing."""
    engine = MultiMaterialCoordinationEngine(mock_recoater_client, mock_job_manager)
    # Set shorter timeouts for testing
    engine.ready_timeout = 5.0
    engine.completion_timeout = 10.0
    engine.status_poll_interval = 0.1
    return engine


@pytest.fixture
def sample_job_state():
    """Create a sample multi-material job state for testing."""
    # Create sample polylines and layers
    points = [Point(x=0.0, y=0.0), Point(x=10.0, y=10.0)]
    polyline = Polyline(part_id=1, direction=0, points=points)
    
    # Create layers for each drum
    layers_drum_0 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline])
    ]
    
    layers_drum_1 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline])
    ]
    
    layers_drum_2 = [
        CliLayer(z_height=0.1, polylines=[polyline])
    ]
    
    # Create layer data for each drum
    from app.models.multilayer_job import LayerData

    remaining_layers = {
        0: [
            LayerData(layer_number=1, cli_data=b"$$LAYER 0.1\n$$POLYLINE\n0.0 0.0\n10.0 10.0\n"),
            LayerData(layer_number=2, cli_data=b"$$LAYER 0.2\n$$POLYLINE\n0.0 0.0\n10.0 10.0\n")
        ],
        1: [
            LayerData(layer_number=1, cli_data=b"$$LAYER 0.1\n$$POLYLINE\n0.0 0.0\n10.0 10.0\n"),
            LayerData(layer_number=2, cli_data=b"$$LAYER 0.2\n$$POLYLINE\n0.0 0.0\n10.0 10.0\n")
        ],
        2: [
            LayerData(layer_number=1, cli_data=b"$$LAYER 0.1\n$$POLYLINE\n0.0 0.0\n10.0 10.0\n")
        ]
    }

    # Create job state
    job_state = MultiMaterialJobState(
        job_id="test-job-001",
        total_layers=2,
        current_layer=1,
        remaining_layers=remaining_layers,
        status=JobStatus.RUNNING,
        is_active=True
    )
    
    return job_state


class TestCoordinationEngineInitialization:
    """Test coordination engine initialization and basic functionality."""
    
    def test_initialization(self, coordination_engine):
        """Test that coordination engine initializes correctly."""
        assert coordination_engine.state == CoordinationState.IDLE
        assert coordination_engine.current_job is None
        assert coordination_engine.error_count == 0
    
    def test_get_coordination_status_idle(self, coordination_engine):
        """Test getting status when engine is idle."""
        status = coordination_engine.get_coordination_status()
        
        assert status["state"] == "idle"
        assert status["error_count"] == 0
        assert status["current_job"] is None


class TestJobStartAndStop:
    """Test job start and stop functionality."""
    
    @pytest.mark.asyncio
    async def test_start_job_success(self, coordination_engine, sample_job_state):
        """Test successful job start."""
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.set_job_active = AsyncMock()
            mock_opcua.update_layer_progress = AsyncMock()
            mock_opcua.clear_error_flags = AsyncMock()
            
            result = await coordination_engine.start_multimaterial_job(sample_job_state)
            
            assert result is True
            assert coordination_engine.current_job == sample_job_state
            assert coordination_engine.state == CoordinationState.UPLOADING
            
            # Verify OPC UA calls
            mock_opcua.set_job_active.assert_called_once_with(sample_job_state.total_layers)
            mock_opcua.update_layer_progress.assert_called_once_with(sample_job_state.current_layer)
            mock_opcua.clear_error_flags.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_job_when_not_idle(self, coordination_engine, sample_job_state):
        """Test that starting a job when not idle raises error."""
        coordination_engine.state = CoordinationState.UPLOADING
        
        with pytest.raises(CoordinationError, match="Cannot start job: engine in state CoordinationState.UPLOADING"):
            await coordination_engine.start_multimaterial_job(sample_job_state)
    
    @pytest.mark.asyncio
    async def test_stop_job(self, coordination_engine, sample_job_state):
        """Test stopping a job."""
        coordination_engine.current_job = sample_job_state
        coordination_engine.state = CoordinationState.UPLOADING
        
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.set_job_inactive = AsyncMock()
            
            result = await coordination_engine.stop_job()
            
            assert result is True
            assert coordination_engine.current_job is None
            assert coordination_engine.state == CoordinationState.IDLE
            assert sample_job_state.status == JobStatus.CANCELLED
            assert sample_job_state.is_active is False
            
            mock_opcua.set_job_inactive.assert_called_once()


class TestMultiDrumUpload:
    """Test multi-drum upload coordination."""
    
    @pytest.mark.asyncio
    async def test_upload_layer_to_all_drums_success(self, coordination_engine, sample_job_state):
        """Test successful upload to all drums."""
        coordination_engine.current_job = sample_job_state
        
        result = await coordination_engine._upload_layer_to_all_drums()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_upload_layer_with_drum_failure(self, coordination_engine, sample_job_state):
        """Test upload with one drum failing."""
        coordination_engine.current_job = sample_job_state
        
        # Mock one drum to fail
        original_upload = coordination_engine.recoater_client.upload_cli_data
        
        async def mock_upload_with_failure(drum_id, cli_data):
            if drum_id == 1:
                raise Exception("Drum 1 upload failed")
            return await original_upload(drum_id, cli_data)
        
        coordination_engine.recoater_client.upload_cli_data = mock_upload_with_failure
        
        result = await coordination_engine._upload_layer_to_all_drums()
        
        assert result is False


class TestDrumStatusMonitoring:
    """Test drum status monitoring and readiness detection."""
    
    @pytest.mark.asyncio
    async def test_wait_for_all_drums_ready_success(self, coordination_engine):
        """Test waiting for all drums to be ready."""
        # All drums start ready in mock client
        result = await coordination_engine._wait_for_all_drums_ready()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_wait_for_drums_ready_timeout(self, coordination_engine):
        """Test timeout when drums don't become ready."""
        # Set a drum to not ready state
        coordination_engine.recoater_client._drum_states[1] = "error"
        
        # Set very short timeout for test
        coordination_engine.ready_timeout = 0.2
        
        result = await coordination_engine._wait_for_all_drums_ready()
        
        assert result is False


class TestLayerCompletion:
    """Test layer completion monitoring."""
    
    @pytest.mark.asyncio
    async def test_wait_for_layer_completion_success(self, coordination_engine):
        """Test successful layer completion."""
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.get_backend_error = AsyncMock(return_value=False)
            mock_opcua.get_plc_error = AsyncMock(return_value=False)
            
            result = await coordination_engine._wait_for_layer_completion()
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_wait_for_layer_completion_backend_error(self, coordination_engine):
        """Test layer completion with backend error."""
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.get_backend_error = AsyncMock(return_value=True)
            mock_opcua.get_plc_error = AsyncMock(return_value=False)
            
            result = await coordination_engine._wait_for_layer_completion()
            
            assert result is False
            assert coordination_engine.state == CoordinationState.ERROR


class TestErrorHandling:
    """Test comprehensive error handling scenarios."""
    
    @pytest.mark.asyncio
    async def test_clear_errors(self, coordination_engine):
        """Test clearing error state."""
        coordination_engine.state = CoordinationState.ERROR
        coordination_engine.error_count = 5
        
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.clear_error_flags = AsyncMock()
            
            result = await coordination_engine.clear_errors()
            
            assert result is True
            assert coordination_engine.error_count == 0
            assert coordination_engine.state == CoordinationState.IDLE
            
            mock_opcua.clear_error_flags.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_set_error_state(self, coordination_engine, sample_job_state):
        """Test setting error state."""
        coordination_engine.current_job = sample_job_state
        
        with patch('app.services.coordination_engine.opcua_coordinator') as mock_opcua:
            mock_opcua.set_backend_error = AsyncMock()
            
            await coordination_engine._set_error_state("Test error")
            
            assert coordination_engine.state == CoordinationState.ERROR
            assert sample_job_state.status == JobStatus.ERROR
            assert sample_job_state.is_active is False
            
            mock_opcua.set_backend_error.assert_called_once_with(True)
