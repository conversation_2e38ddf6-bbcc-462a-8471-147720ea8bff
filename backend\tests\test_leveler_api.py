"""
Test suite for leveler control API endpoints.

This module tests the leveler pressure and sensor control endpoints
to ensure they work correctly with mocked RecoaterClient.
"""

import pytest
from unittest.mock import Mock
from fastapi.testclient import TestClient

from app.main import app
from infrastructure.recoater_client import RecoaterConnectionError, RecoaterAPIError


class TestLevelerPressureAPI:
    """Test cases for leveler pressure endpoints."""

    def test_get_leveler_pressure_success(self, mock_recoater_client):
        """Test successful retrieval of leveler pressure."""
        # Arrange
        mock_pressure_data = {
            "maximum": 10.0,
            "target": 5.0,
            "value": 4.8
        }
        mock_recoater_client.get_leveler_pressure.return_value = mock_pressure_data

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/pressure")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert data["leveler_pressure"] == mock_pressure_data
        mock_recoater_client.get_leveler_pressure.assert_called_once()

    def test_get_leveler_pressure_connection_error(self, mock_recoater_client):
        """Test leveler pressure retrieval with connection error."""
        # Arrange
        mock_recoater_client.get_leveler_pressure.side_effect = RecoaterConnectionError("Connection failed")

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/pressure")

        # Assert
        assert response.status_code == 503
        assert "Connection to recoater failed" in response.json()["detail"]

    def test_get_leveler_pressure_api_error(self, mock_recoater_client):
        """Test leveler pressure retrieval with API error."""
        # Arrange
        mock_recoater_client.get_leveler_pressure.side_effect = RecoaterAPIError("API error")

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/pressure")

        # Assert
        assert response.status_code == 400
        assert "Recoater API error" in response.json()["detail"]

    def test_set_leveler_pressure_success(self, mock_recoater_client):
        """Test successful setting of leveler pressure."""
        # Arrange
        target_pressure = 6.5
        mock_response = {
            "success": True,
            "target_pressure": target_pressure,
            "unit": "Pa"
        }
        mock_recoater_client.set_leveler_pressure.return_value = mock_response

        # Act
        with TestClient(app) as client:
            response = client.put(
                "/api/v1/recoater/leveler/pressure",
                json={"target": target_pressure}
            )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert data["leveler_command"]["target"] == target_pressure
        assert data["response"] == mock_response
        mock_recoater_client.set_leveler_pressure.assert_called_once_with(target=target_pressure)

    def test_set_leveler_pressure_invalid_data(self, mock_recoater_client):
        """Test setting leveler pressure with invalid data."""
        # Act
        with TestClient(app) as client:
            response = client.put(
                "/api/v1/recoater/leveler/pressure",
                json={"target": -1.0}  # Negative pressure should be invalid
            )

        # Assert
        assert response.status_code == 422  # Validation error

    def test_set_leveler_pressure_connection_error(self, mock_recoater_client):
        """Test setting leveler pressure with connection error."""
        # Arrange
        mock_recoater_client.set_leveler_pressure.side_effect = RecoaterConnectionError("Connection failed")

        # Act
        with TestClient(app) as client:
            response = client.put(
                "/api/v1/recoater/leveler/pressure",
                json={"target": 5.0}
            )

        # Assert
        assert response.status_code == 503
        assert "Connection to recoater failed" in response.json()["detail"]


class TestLevelerSensorAPI:
    """Test cases for leveler sensor endpoints."""

    def test_get_leveler_sensor_success(self, mock_recoater_client):
        """Test successful retrieval of leveler sensor state."""
        # Arrange
        mock_sensor_data = {"state": True}
        mock_recoater_client.get_leveler_sensor.return_value = mock_sensor_data

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/sensor")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert data["leveler_sensor"] == mock_sensor_data
        mock_recoater_client.get_leveler_sensor.assert_called_once()

    def test_get_leveler_sensor_connection_error(self, mock_recoater_client):
        """Test leveler sensor retrieval with connection error."""
        # Arrange
        mock_recoater_client.get_leveler_sensor.side_effect = RecoaterConnectionError("Connection failed")

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/sensor")

        # Assert
        assert response.status_code == 503
        assert "Connection to recoater failed" in response.json()["detail"]

    def test_get_leveler_sensor_api_error(self, mock_recoater_client):
        """Test leveler sensor retrieval with API error."""
        # Arrange
        mock_recoater_client.get_leveler_sensor.side_effect = RecoaterAPIError("API error")

        # Act
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/leveler/sensor")

        # Assert
        assert response.status_code == 400
        assert "Recoater API error" in response.json()["detail"]
