"""
Leveler Control Methods
======================

This module contains leveler control methods for the RecoaterClient.
These methods implement the leveler control endpoints from the openapi.json specification.
"""

from typing import Dict, Any


class LevelerControlMixin:
    """Mixin class providing leveler control methods for RecoaterClient."""

    def get_leveler_pressure(self) -> Dict[str, Any]:
        """
        Get the leveler pressure information.

        Returns:
            Dictionary containing leveler pressure information (maximum, target, value)
        """
        return self._make_request("GET", "/leveler/pressure")

    def set_leveler_pressure(self, target: float) -> Dict[str, Any]:
        """
        Set the target pressure for the leveler.

        Args:
            target: Target leveler pressure [Pa]

        Returns:
            Response from the API
        """
        payload = {"target": target}
        return self._make_request("PUT", "/leveler/pressure", json=payload)

    def get_leveler_sensor(self) -> Dict[str, Any]:
        """
        Get the current state of the magnetic sensor on the leveler.

        Returns:
            Dictionary containing leveler sensor state
        """
        return self._make_request("GET", "/leveler/sensor")
