"""Integration tests for CLI upload -> Multi-material job -> OPC UA variable updates.

These tests exercise the real OPC UA server (asyncua) started via the global
`opcua_coordinator` and verify that starting a multi-material job after
uploading CLI file(s) correctly updates coordination variables:
  - job_active
  - total_layers (auto-detected max layer count)
  - current_layer (set to 1 after job start)

They also validate that single-file job creation (auto-filling empty templates)
and dual-file job creation both set the expected total_layers.
"""

import asyncio
import uuid
import pytest
from io import BytesIO
from httpx import AsyncClient, ASGITransport
from app import dependencies as deps

from app.main import app
from app.services.opcua_coordinator import opcua_coordinator


def _build_cli_content(layer_count: int) -> bytes:
    """Build a minimal valid ASCII CLI content with the given number of layers."""
    header = [
        "$$HEADERSTART",
        "$$ASCII",
        "$$UNITS/00000000.005000",
        "$$VERSION/200",
        f"$$LABEL/1,test_{layer_count}",
        f"$$LAYERS/{layer_count:06d}",
        "$$HEADEREND",
        "$$GEOMETRYSTART",
    ]
    body = []
    for i in range(layer_count):
        z = f"{i*0.1:.1f}"
        body.append(f"$$LAYER/{z}")
        # Simple square polyline (5 points) referencing part 1
        body.append("$$POLYLINE/1,0,0,0,0")
        body.append("0.0,0.0")
        body.append("10.0,0.0")
        body.append("10.0,10.0")
        body.append("0.0,10.0")
        body.append("0.0,0.0")
    footer = ["$$GEOMETRYEND"]
    content = "\n".join(header + body + footer)
    return content.encode("utf-8")


@pytest.mark.asyncio
async def test_single_file_job_updates_opcua_variables():
    """Upload a single CLI file, start job, verify OPC UA variables reflect auto-detected layers."""
    # Ensure OPC UA coordinator connected
    connected = await opcua_coordinator.connect()
    assert connected, "Failed to connect OPC UA coordinator"

    layer_count = 5
    cli_bytes = _build_cli_content(layer_count)

    # Ensure multilayer job manager initialized (app lifespan not auto-run here)
    deps.initialize_multilayer_job_manager()
    # Use ASGITransport without deprecated 'app' arg on AsyncClient
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://testserver") as client:
        file_id = None
        # Upload CLI file
        files = {"file": (f"single_{uuid.uuid4().hex[:6]}.cli", BytesIO(cli_bytes), "application/octet-stream")}
        resp = await client.post("/api/v1/print/cli/upload", files=files)
        assert resp.status_code == 200, resp.text
        upload_data = resp.json()
        assert upload_data["total_layers"] == layer_count
        file_id = upload_data["file_id"]

        # Start multi-material job with single file (auto-fills empty templates)
        start_resp = await client.post(
            "/api/v1/print/cli/start-multimaterial-job",
            json={"file_ids": {"0": file_id}}
        )
        assert start_resp.status_code == 200, start_resp.text
        start_data = start_resp.json()
        assert start_data["success"] is True

        # Allow small delay for async OPC UA writes
        await asyncio.sleep(0.5)

        # Verify OPC UA variables
        job_active = await opcua_coordinator.read_variable("job_active")
        total_layers = await opcua_coordinator.read_variable("total_layers")
        current_layer = await opcua_coordinator.read_variable("current_layer")

        assert job_active is True
        assert total_layers == layer_count
        # After start_job, layer progress set to 1
        assert current_layer == 1

    # Cleanup
    await opcua_coordinator.set_job_inactive()
    await opcua_coordinator.disconnect()
    await asyncio.sleep(0.1)


@pytest.mark.asyncio
async def test_dual_file_job_max_layers_detected():
    """Upload two CLI files with different layer counts; verify total_layers = max count in OPC UA variables."""
    connected = await opcua_coordinator.connect()
    assert connected, "Failed to connect OPC UA coordinator"

    cli_a = _build_cli_content(3)  # 3 layers
    cli_b = _build_cli_content(7)  # 7 layers (expected total)

    deps.initialize_multilayer_job_manager()
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://testserver") as client:
        # Upload both files
        resp_a = await client.post("/api/v1/print/cli/upload", files={"file": ("a.cli", BytesIO(cli_a), "application/octet-stream")})
        resp_b = await client.post("/api/v1/print/cli/upload", files={"file": ("b.cli", BytesIO(cli_b), "application/octet-stream")})
        assert resp_a.status_code == 200 and resp_b.status_code == 200
        file_id_a = resp_a.json()["file_id"]
        file_id_b = resp_b.json()["file_id"]

        # Start job with two files (drums 0 and 1)
        start_resp = await client.post(
            "/api/v1/print/cli/start-multimaterial-job",
            json={"file_ids": {"0": file_id_a, "1": file_id_b}}
        )
        assert start_resp.status_code == 200, start_resp.text
        assert start_resp.json()["success"] is True

        await asyncio.sleep(0.5)

        total_layers = await opcua_coordinator.read_variable("total_layers")
        current_layer = await opcua_coordinator.read_variable("current_layer")
        assert total_layers == 7
        assert current_layer == 1

    await opcua_coordinator.set_job_inactive()
    await opcua_coordinator.disconnect()
    await asyncio.sleep(0.1)
