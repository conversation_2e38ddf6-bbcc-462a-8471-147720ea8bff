"""
Recoater Client Package
======================

This package provides the RecoaterClient class and related components for
communicating with the Aerosint SPD Recoater hardware API.

The package is organized into the following modules:
- client: Main RecoaterClient class
- exceptions: Custom exception classes
- async_client: Async wrapper methods for multi-material coordination
"""

from .exceptions import RecoaterConnectionError, RecoaterAPIError
from .client import RecoaterClient

__all__ = [
    'RecoaterClient',
    'RecoaterConnectionError', 
    'RecoaterAPIError'
]
