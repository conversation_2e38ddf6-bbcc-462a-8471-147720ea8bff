import struct
import io
import logging
from typing import List, Optional
from .cli_models import <PERSON>, <PERSON><PERSON><PERSON>, Hatch, <PERSON>li<PERSON>ayer, ParsedCliFile
from .cli_exceptions import CliParsingError

class CliFileParser:
    """Handles parsing of CLI files (ASCII and binary)."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

    def _read_and_unpack(self, stream: io.BytesIO, fmt: str, size: int) -> tuple:
        """Reads bytes from a stream and unpacks them."""
        data = stream.read(size)
        if len(data) < size:
            raise EOFError(f"Unexpected EOF. Wanted {size} bytes, got {len(data)}.")
        return struct.unpack(fmt, data)        

    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Auto‐detect ASCII vs. binary CLI and delegate."""
        # Better detection logic: look for ASCII-specific patterns after header
        try:
            text = cli_byte_stream.decode("ascii", errors="strict")
            # Look for ASCII CLI patterns (commands followed by parameters on same line)
            # Support both space and slash delimited formats
            if "$$HEADEREND" in text and ("$$LAYER " in text or "$$POLYLINE " in text or "$$LAYER/" in text or "$$POLYLINE/" in text):
                return self._parse_ascii(text)
        except UnicodeDecodeError:
            pass  # Definitely binary
        
        # Default to binary parsing
        return self._parse_binary(cli_byte_stream)

    # Backwards compatibility wrapper (legacy code expects parse_cli_file(file_like))
    def parse_cli_file(self, file_obj) -> ParsedCliFile:  # pragma: no cover - thin wrapper
        """Parse a CLI file from a file-like object.

        Existing code paths (e.g., MultiMaterialJobManager._load_empty_layer_template and
        older tests) referenced a parse_cli_file method. That method was renamed to
        'parse' in a refactor. We provide this wrapper to avoid changing all call sites.

        Args:
            file_obj: A file-like object supporting .read(). May return str or bytes.

        Returns:
            ParsedCliFile: Structured representation of the CLI file.
        """
        try:
            data = file_obj.read()
        except Exception as e:  # pragma: no cover - defensive
            raise CliParsingError(f"Failed reading CLI file-like object: {e}")

        if isinstance(data, str):
            data = data.encode('utf-8')
        if not isinstance(data, (bytes, bytearray)):
            raise CliParsingError("CLI parser expected bytes or string content from file-like object")
        return self.parse(data)

    # (you can leave _looks_like_ascii around as a fallback or deprecate it)
    def _looks_like_ascii(self, data: bytes) -> bool:
        text = data.decode("ascii", errors="ignore")
        return "$$HEADEREND" in text and ("$$LAYER" in text or "$$POLYLINE" in text)

    def _parse_ascii(self, text: str) -> ParsedCliFile:
        """Line‐by‐line CLI parser for ASCII .cli files with better error handling.
        Supports both space-delimited and slash-delimited formats."""
        lines = [l.strip() for l in text.splitlines() if l.strip()]
        header_lines: List[str] = []
        geom_lines: List[str] = []
        seen_header_end = False
        seen_geometry_start = False

        # split header vs geometry
        for l in lines:
            if not seen_header_end:
                header_lines.append(l)
                if l.upper() == "$$HEADEREND":
                    seen_header_end = True
                continue
            # Skip $$GEOMETRYSTART if present
            if l.upper() == "$$GEOMETRYSTART":
                seen_geometry_start = True
                continue
            geom_lines.append(l)

        is_aligned = any("$$ALIGN" in h.upper() for h in header_lines)
        layers: List[CliLayer] = []
        current: Optional[CliLayer] = None
        i = 0

        def parse_command_line(line: str) -> tuple:
            """Parse a command line that can be either space or slash delimited."""
            if '/' in line and line.startswith('$$'):
                # Slash-delimited format: $$LAYER/16.0
                parts = line.split('/', 1)
                if len(parts) == 2:
                    cmd = parts[0].upper()
                    param_str = parts[1]
                    # For commands that need multiple parameters, split by comma or space
                    if cmd in ["$$POLYLINE", "$$HATCHES"]:
                        params = param_str.replace(',', ' ').split()
                        return [cmd] + params
                    else:
                        return [cmd, param_str]
                else:
                    return [line.upper()]
            else:
                # Space-delimited format: $$LAYER 16.0
                return line.split()

        while i < len(geom_lines):
            line = geom_lines[i]
            parts = parse_command_line(line)
            
            if not parts:  # Skip empty lines
                i += 1
                continue
                
            cmd = parts[0].upper()
            
            try:
                if cmd == "$$LAYER":
                    if len(parts) < 2:
                        raise CliParsingError(f"Invalid $$LAYER directive at line {i}: '{line}' - missing Z height")
                    z = float(parts[1])
                    current = CliLayer(z_height=z, polylines=[], hatches=[])
                    layers.append(current)
                    i += 1

                elif cmd == "$$POLYLINE":
                    if not current:
                        raise CliParsingError(f"Found $$POLYLINE at line {i} before any $$LAYER")
                    if len(parts) < 4:
                        raise CliParsingError(f"Invalid $$POLYLINE directive at line {i}: '{line}' - expected 3 args")
                    
                    try:
                        # Check if coordinates are inline (more than 4 parts) or on separate lines
                        if len(parts) > 4:
                            # Inline format: $$POLYLINE/part_id,direction,count,x1,y1,x2,y2,...
                            pid, dr, cnt = int(parts[1]), int(parts[2]), int(parts[3])
                            coord_parts = parts[4:]  # All coordinate values
                            
                            if len(coord_parts) < cnt * 2:
                                raise CliParsingError(f"Insufficient coordinates in polyline at line {i}: expected {cnt * 2} coordinates, got {len(coord_parts)}")
                            
                            pts: List[Point] = []
                            for coord_idx in range(0, cnt * 2, 2):
                                try:
                                    x = float(coord_parts[coord_idx])
                                    y = float(coord_parts[coord_idx + 1])
                                    pts.append(Point(x=x, y=y))
                                except (ValueError, IndexError) as e:
                                    raise CliParsingError(f"Invalid coordinates in polyline at line {i}: {e}")
                            
                            current.polylines.append(Polyline(part_id=pid, direction=dr, points=pts))
                            i += 1  # Only advance by 1 since all data is on current line
                            
                        else:
                            # Multi-line format: $$POLYLINE part_id direction count (coordinates on separate lines)
                            _, pid_s, dr_s, cnt_s = parts
                            pid, dr, cnt = int(pid_s), int(dr_s), int(cnt_s)
                            
                            pts: List[Point] = []
                            # read the next cnt lines as point coords
                            for j in range(1, cnt + 1):
                                point_line_idx = i + j
                                if point_line_idx >= len(geom_lines):
                                    raise CliParsingError(f"Unexpected EOF in polyline points at line {i}. Expected {cnt} points, but only found {j-1} points. Missing {cnt - (j-1)} points.")
                                
                                point_line = geom_lines[point_line_idx]
                                xy = point_line.replace(',', ' ').split()  # Handle comma-separated coordinates
                                if len(xy) < 2:
                                    raise CliParsingError(f"Bad polyline point at line {point_line_idx}: '{point_line}' - expected X Y coordinates")
                                
                                try:
                                    x, y = float(xy[0]), float(xy[1])
                                    pts.append(Point(x=x, y=y))
                                except ValueError as e:
                                    raise CliParsingError(f"Invalid coordinates at line {point_line_idx}: '{point_line}' - {e}")
                            
                            current.polylines.append(Polyline(part_id=pid, direction=dr, points=pts))
                            i += 1 + cnt  # Advance by 1 + number of coordinate lines
                            
                    except ValueError as e:
                        raise CliParsingError(f"Invalid $$POLYLINE parameters at line {i}: '{line}' - {e}")

                elif cmd == "$$HATCHES":
                    if not current:
                        raise CliParsingError(f"Found $$HATCHES at line {i} before any $$LAYER")
                    if len(parts) < 3:
                        raise CliParsingError(f"Invalid $$HATCHES directive at line {i}: '{line}' - expected 2 args")
                    
                    try:
                        # Check if coordinates are inline (more than 3 parts) or on separate lines
                        if len(parts) > 3:
                            # Inline format: $$HATCHES/group_id,count,x1,y1,x2,y2,x3,y3,x4,y4,...
                            grp, num = int(parts[1]), int(parts[2])
                            coord_parts = parts[3:]  # All coordinate values
                            
                            if len(coord_parts) < num * 4:
                                raise CliParsingError(f"Insufficient coordinates in hatches at line {i}: expected {num * 4} coordinates, got {len(coord_parts)}")
                            
                            lines_list: List[Tuple[Point, Point]] = []
                            for coord_idx in range(0, num * 4, 4):
                                try:
                                    x1 = float(coord_parts[coord_idx])
                                    y1 = float(coord_parts[coord_idx + 1])
                                    x2 = float(coord_parts[coord_idx + 2])
                                    y2 = float(coord_parts[coord_idx + 3])
                                    lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                                except (ValueError, IndexError) as e:
                                    raise CliParsingError(f"Invalid coordinates in hatches at line {i}: {e}")
                            
                            current.hatches.append(Hatch(group_id=grp, lines=lines_list))
                            i += 1  # Only advance by 1 since all data is on current line
                            
                        else:
                            # Multi-line format: $$HATCHES group_id count (coordinates on separate lines)
                            _, grp_s, num_s = parts
                            grp, num = int(grp_s), int(num_s)
                            
                            lines_list: List[Tuple[Point, Point]] = []
                            for j in range(1, num + 1):
                                hatch_line_idx = i + j
                                if hatch_line_idx >= len(geom_lines):
                                    raise CliParsingError(f"Unexpected EOF in hatches at line {i}. Expected {num} hatch lines, but only found {j-1}.")
                                
                                hatch_line = geom_lines[hatch_line_idx]
                                coords = hatch_line.replace(',', ' ').split()  # Handle comma-separated coordinates
                                if len(coords) < 4:
                                    raise CliParsingError(f"Bad hatch line at line {hatch_line_idx}: '{hatch_line}' - expected X1 Y1 X2 Y2 coordinates")
                                
                                try:
                                    x1, y1, x2, y2 = map(float, coords[:4])
                                    lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                                except ValueError as e:
                                    raise CliParsingError(f"Invalid hatch coordinates at line {hatch_line_idx}: '{hatch_line}' - {e}")
                            
                            current.hatches.append(Hatch(group_id=grp, lines=lines_list))
                            i += 1 + num  # Advance by 1 + number of coordinate lines
                            
                    except ValueError as e:
                        raise CliParsingError(f"Invalid $$HATCHES parameters at line {i}: '{line}' - {e}")

                else:
                    # unrecognized directive, skip with warning
                    self.logger.warning(f"Unrecognized directive at line {i}: '{line}' - skipping")
                    i += 1
                    
            except CliParsingError:
                raise  # Re-raise CLI parsing errors
            except Exception as e:
                raise CliParsingError(f"Unexpected error parsing line {i}: '{line}' - {e}")

        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)

    def _parse_binary(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """
        Parses a binary CLI file from an in-memory byte stream.
        Fixed version with better header handling.
        """
        stream = io.BytesIO(cli_byte_stream)

        # 1. Parse Header - More robust approach
        header_end_marker = b"$$HEADEREND"
        header_buffer = bytearray()
        max_header_size = 8192
        
        while True:
            byte = stream.read(1)
            if not byte:
                raise CliParsingError(f"EOF reached before '{header_end_marker.decode()}' was found.")
            header_buffer.append(byte[0])
            
            # Check if we found the marker
            if header_end_marker in header_buffer:
                # Find the exact position of the marker
                marker_pos = header_buffer.find(header_end_marker)
                actual_header_bytes = header_buffer[:marker_pos + len(header_end_marker)]
                
                # Set stream position to after the header
                geometry_start_offset = stream.tell() - (len(header_buffer) - len(actual_header_bytes))
                stream.seek(geometry_start_offset)
                break
                
            if len(header_buffer) > max_header_size:
                raise CliParsingError(f"Header size exceeds {max_header_size} bytes limit.")

        # Process header
        try:
            header_str = header_buffer[:marker_pos + len(header_end_marker)].decode('ascii', errors='replace')
        except UnicodeDecodeError as e:
            self.logger.warning(f"Unicode decode error in header: {e}. Using replacement characters.")
            header_str = header_buffer[:marker_pos + len(header_end_marker)].decode('ascii', errors='replace')
        
        header_lines = [line.strip() for line in header_str.splitlines() if line.strip()]
        is_aligned = any("$$ALIGN" in line.upper() for line in header_lines)
        self.logger.info(f"CLI header parsed. Alignment detected: {is_aligned}")

        # 2. Parse Geometry
        layers: List[CliLayer] = []
        current_layer: Optional[CliLayer] = None

        while True:
            try:
                command_code = self._read_and_unpack(stream, "<H", 2)[0]
                if is_aligned:
                    stream.read(2)  # Skip 2 alignment bytes

                if command_code == 127:  # Layer
                    z = self._read_and_unpack(stream, "<f", 4)[0]
                    current_layer = CliLayer(z_height=z)
                    layers.append(current_layer)
                
                elif command_code == 130:  # Polyline
                    if not current_layer:
                        raise CliParsingError("Found Polyline data before a Layer was defined.")
                    part_id, direction, num_points = self._read_and_unpack(stream, "<iii", 12)
                    points = []
                    for _ in range(num_points):
                        x = self._read_and_unpack(stream, "<f", 4)[0]
                        y = self._read_and_unpack(stream, "<f", 4)[0]
                        points.append(Point(x=x, y=y))
                    current_layer.polylines.append(Polyline(part_id=part_id, direction=direction, points=points))

                elif command_code == 132:  # Hatches
                    if not current_layer:
                        raise CliParsingError("Found Hatch data before a Layer was defined.")
                    group_id, num_lines = self._read_and_unpack(stream, "<ii", 8)
                    hatches = []
                    for _ in range(num_lines):
                        x1, y1, x2, y2 = self._read_and_unpack(stream, "<ffff", 16)
                        hatches.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                    current_layer.hatches.append(Hatch(group_id=group_id, lines=hatches))
                
                else:
                    self.logger.warning(f"Unsupported command code {command_code} at offset {stream.tell()}. Stopping parse.")
                    break

            except EOFError:
                self.logger.info("Successfully reached end of CLI geometry data.")
                break
            except struct.error as e:
                raise CliParsingError(f"Struct unpacking error at offset {stream.tell()}: {e}")

        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)