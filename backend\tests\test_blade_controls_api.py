"""
Tests for the blade control API endpoints.

This module contains tests for the blade control API endpoints in the recoater_controls.py module.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock

from app.main import app
from infrastructure.recoater_client import RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client for testing."""
    mock_client = MagicMock()

    def get_mock_client():
        return mock_client

    # Override the dependency
    app.dependency_overrides[get_recoater_client] = get_mock_client

    yield mock_client

    # Clean up
    app.dependency_overrides.clear()


class TestBladeControlsAPI:
    """Tests for the blade control API endpoints."""

    def test_get_blade_screws_info_success(self, mock_recoater_client):
        """Test getting blade screws info successfully."""
        # Setup mock response
        mock_recoater_client.get_blade_screws_info.return_value = [
            {"id": 0, "position": 1000.0, "running": False},
            {"id": 1, "position": 2000.0, "running": True}
        ]

        # Make request
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/drums/0/blade/screws")

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert len(response.json()["blade_screws"]) == 2
        assert response.json()["blade_screws"][0]["id"] == 0
        assert response.json()["blade_screws"][1]["id"] == 1

        # Verify mock was called correctly
        mock_recoater_client.get_blade_screws_info.assert_called_once_with(0)

    def test_get_blade_screws_info_connection_error(self, mock_recoater_client):
        """Test getting blade screws info with connection error."""
        # Setup mock to raise connection error
        mock_recoater_client.get_blade_screws_info.side_effect = RecoaterConnectionError("Connection failed")

        # Make request
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/drums/0/blade/screws")

        # Check response
        assert response.status_code == 503
        assert "Failed to connect" in response.json()["detail"]

    def test_get_blade_screws_motion_success(self, mock_recoater_client):
        """Test getting blade screws motion successfully."""
        # Setup mock response
        mock_recoater_client.get_blade_screws_motion.return_value = {
            "mode": "relative",
            "distance": 1500.0
        }

        # Make request
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/drums/0/blade/screws/motion")

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["motion"]["mode"] == "relative"
        assert response.json()["motion"]["distance"] == 1500.0

        # Verify mock was called correctly
        mock_recoater_client.get_blade_screws_motion.assert_called_once_with(0)

    def test_set_blade_screws_motion_success(self, mock_recoater_client):
        """Test setting blade screws motion successfully."""
        # Setup mock response
        mock_recoater_client.set_blade_screws_motion.return_value = {
            "success": True,
            "estimated_time": 5.0
        }

        # Make request
        with TestClient(app) as client:
            response = client.post(
                "/api/v1/recoater/drums/0/blade/screws/motion",
                json={"mode": "relative", "distance": 2000.0}
            )

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["motion_command"]["mode"] == "relative"
        assert response.json()["motion_command"]["distance"] == 2000.0
        assert response.json()["response"]["success"] is True

        # Verify mock was called correctly
        mock_recoater_client.set_blade_screws_motion.assert_called_once_with(
            drum_id=0, mode="relative", distance=2000.0
        )

    def test_set_blade_screws_motion_homing(self, mock_recoater_client):
        """Test setting blade screws motion with homing mode."""
        # Setup mock response
        mock_recoater_client.set_blade_screws_motion.return_value = {
            "success": True,
            "estimated_time": 3.0
        }

        # Make request
        with TestClient(app) as client:
            response = client.post(
                "/api/v1/recoater/drums/0/blade/screws/motion",
                json={"mode": "homing"}
            )

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["motion_command"]["mode"] == "homing"
        # Distance will be None for homing mode, but still present in the response
        assert response.json()["motion_command"]["distance"] is None
        assert response.json()["response"]["success"] is True

        # Verify mock was called correctly
        mock_recoater_client.set_blade_screws_motion.assert_called_once_with(
            drum_id=0, mode="homing", distance=None
        )

    def test_cancel_blade_screws_motion_success(self, mock_recoater_client):
        """Test cancelling blade screws motion successfully."""
        # Setup mock response
        mock_recoater_client.cancel_blade_screws_motion.return_value = {
            "success": True,
            "action": "motion_cancelled"
        }

        # Make request
        with TestClient(app) as client:
            response = client.delete("/api/v1/recoater/drums/0/blade/screws/motion")

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["action"] == "motion_cancelled"
        assert response.json()["response"]["success"] is True

        # Verify mock was called correctly
        mock_recoater_client.cancel_blade_screws_motion.assert_called_once_with(0)

    def test_get_blade_screw_info_success(self, mock_recoater_client):
        """Test getting individual blade screw info successfully."""
        # Setup mock response
        mock_recoater_client.get_blade_screw_info.return_value = {
            "id": 1,
            "position": 3000.0,
            "running": True
        }

        # Make request
        with TestClient(app) as client:
            response = client.get("/api/v1/recoater/drums/0/blade/screws/1")

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["screw_info"]["id"] == 1
        assert response.json()["screw_info"]["position"] == 3000.0
        assert response.json()["screw_info"]["running"] is True

        # Verify mock was called correctly
        mock_recoater_client.get_blade_screw_info.assert_called_once_with(0, 1)

    def test_set_blade_screw_motion_success(self, mock_recoater_client):
        """Test setting individual blade screw motion successfully."""
        # Setup mock response
        mock_recoater_client.set_blade_screw_motion.return_value = {
            "success": True,
            "estimated_time": 2.5
        }

        # Make request
        with TestClient(app) as client:
            response = client.post(
                "/api/v1/recoater/drums/0/blade/screws/1/motion",
                json={"distance": 1500.0}
            )

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["motion_command"]["distance"] == 1500.0
        assert response.json()["response"]["success"] is True

        # Verify mock was called correctly
        mock_recoater_client.set_blade_screw_motion.assert_called_once_with(
            drum_id=0, screw_id=1, distance=1500.0
        )

    def test_cancel_blade_screw_motion_success(self, mock_recoater_client):
        """Test cancelling individual blade screw motion successfully."""
        # Setup mock response
        mock_recoater_client.cancel_blade_screw_motion.return_value = {
            "success": True,
            "action": "motion_cancelled"
        }

        # Make request
        with TestClient(app) as client:
            response = client.delete("/api/v1/recoater/drums/0/blade/screws/1/motion")

        # Check response
        assert response.status_code == 200
        assert response.json()["connected"] is True
        assert response.json()["action"] == "motion_cancelled"
        assert response.json()["response"]["success"] is True

        # Verify mock was called correctly
        mock_recoater_client.cancel_blade_screw_motion.assert_called_once_with(0, 1)

    def test_invalid_blade_motion_data(self, mock_recoater_client):
        """Test setting blade screws motion with invalid data."""
        # Make request with invalid mode
        with TestClient(app) as client:
            response = client.post(
                "/api/v1/recoater/drums/0/blade/screws/motion",
                json={"mode": "invalid_mode", "distance": 1000.0}
            )

        # Check response
        assert response.status_code == 422
        assert "Input should be 'absolute', 'relative' or 'homing'" in response.json()["detail"][0]["msg"]

    def test_invalid_blade_screw_motion_data(self, mock_recoater_client):
        """Test setting individual blade screw motion with invalid data."""
        # Make request with missing distance
        with TestClient(app) as client:
            response = client.post(
                "/api/v1/recoater/drums/0/blade/screws/1/motion",
                json={}
            )

        # Check response
        assert response.status_code == 422
        assert "Field required" in response.json()["detail"][0]["msg"]
