"""
Print Control Methods
====================

This module contains print control methods for the RecoaterClient.
These methods implement the print control endpoints from the openapi.json specification.
"""

from typing import Dict, Any


class PrintControlMixin:
    """Mixin class providing print control methods for RecoaterClient."""

    def get_layer_parameters(self) -> Dict[str, Any]:
        """
        Get the current parameters of the layer.

        Returns:
            Dictionary containing layer parameters (filling_id, speed, powder_saving, x_offset, max_x_offset)
        """
        return self._make_request("GET", "/layer/parameters")

    def set_layer_parameters(self, filling_id: int, speed: float, powder_saving: bool = True, x_offset: float = None) -> Dict[str, Any]:
        """
        Set the parameters of the current layer.

        Args:
            filling_id: The ID of the drum with the filling material powder. Set to -1 for no filling.
            speed: The patterning speed [mm/s]
            powder_saving: Flag indicating if powder saving strategies are used (default: True)
            x_offset: The offset along the X axis [mm] (optional)

        Returns:
            Response from the API
        """
        payload = {
            "filling_id": filling_id,
            "speed": speed,
            "powder_saving": powder_saving
        }
        if x_offset is not None:
            payload["x_offset"] = x_offset

        return self._make_request("PUT", "/layer/parameters", json=payload)

    def get_layer_preview(self) -> bytes:
        """
        Get layer preview as PNG image.

        Returns:
            PNG image data as bytes
        """
        response = self._make_request("GET", "/layer/preview", return_raw=True)
        return response.content

    def start_print_job(self) -> Dict[str, Any]:
        """
        Create a printing job if the server is ready to start.

        Returns:
            Response from the API
        """
        return self._make_request("POST", "/print/job")

    def cancel_print_job(self) -> Dict[str, Any]:
        """
        Cancel and remove the current printing job.

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", "/print/job")

    def get_print_job_status(self) -> Dict[str, Any]:
        """
        Get the current print job status by checking the recoater state.

        Returns:
            Dictionary containing print job status information
        """
        state_response = self.get_state()
        return {
            "state": state_response.get("state", "unknown"),
            "is_printing": state_response.get("state") == "printing",
            "has_error": state_response.get("state") == "error"
        }
