"""
Async Client Methods
===================

This module contains async wrapper methods for the RecoaterClient.
These methods support Stage 3 coordination logic for multi-drum operations.

!!! Some of the methods in this script should NOT be here. !!!
Refactor methods like get_multimaterial_status() and monitor_drum_state_transitions()
into the service layer (E.g. app/services/multilayer_job_manager.py)
"""

import time
import asyncio
import logging
from typing import Dict, Any, List

from .exceptions import RecoaterConnectionError

logger = logging.getLogger(__name__)


class AsyncClientMixin:
    """Mixin class providing async methods for RecoaterClient."""

    async def upload_cli_data(self, drum_id: int, cli_data: bytes) -> Dict[str, Any]:
        """
        Upload CLI data to a specific drum (async wrapper).

        Args:
            drum_id: Target drum (0, 1, or 2)
            cli_data: ASCII CLI data to upload

        Returns:
            Response from the API

        Raises:
            RecoaterConnectionError: If connection fails
            RecoaterAPIError: If API returns error
        """
        # Use asyncio.to_thread to make the synchronous call async
        return await asyncio.to_thread(
            self.upload_drum_geometry,
            drum_id,
            cli_data,
            "application/octet-stream"
        )

    async def get_drum_status(self, drum_id: int) -> Dict[str, Any]:
        """
        Get status for a specific drum (async wrapper).

        Args:
            drum_id: Target drum (0, 1, or 2)

        Returns:
            Dictionary containing drum status with 'ready' field

        Raises:
            RecoaterConnectionError: If connection fails
            RecoaterAPIError: If API returns error
        """
        # Get drum information
        drum_info = await asyncio.to_thread(self.get_drum, drum_id)

        # Parse status to determine if drum is ready
        # This is a simplified implementation - actual logic would depend on hardware API
        state = drum_info.get("state", "unknown")
        ready = state in ["ready", "idle", "standby"]

        return {
            "drum_id": drum_id,
            "state": state,
            "ready": ready,
            "raw_info": drum_info
        }

    async def get_multimaterial_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status for all drums in multi-material operation.

        Returns:
            Dictionary containing status for all 3 drums
        """
        # Get status for all drums concurrently
        drum_status_tasks = [
            self.get_drum_status(drum_id) for drum_id in [0, 1, 2]
        ]

        try:
            drum_statuses = await asyncio.gather(*drum_status_tasks, return_exceptions=True)

            # Process results
            result = {
                "timestamp": time.time(),
                "drums": {},
                "all_ready": True,
                "errors": []
            }

            for drum_id, status in enumerate(drum_statuses):
                if isinstance(status, Exception):
                    result["drums"][str(drum_id)] = {
                        "error": str(status),
                        "ready": False
                    }
                    result["all_ready"] = False
                    result["errors"].append(f"Drum {drum_id}: {status}")
                else:
                    result["drums"][str(drum_id)] = status
                    if not status.get("ready", False):
                        result["all_ready"] = False

            return result

        except Exception as e:
            logger.error(f"Failed to get multi-material status: {e}")
            raise RecoaterConnectionError(f"Multi-material status check failed: {e}")

    async def monitor_drum_state_transitions(self, drum_id: int, expected_states: List[str],
                                           timeout: float = 30.0, poll_interval: float = 1.0) -> bool:
        """
        Monitor a drum for specific state transitions.

        Args:
            drum_id: Target drum (0, 1, or 2)
            expected_states: List of states to wait for (in order)
            timeout: Maximum time to wait (seconds)
            poll_interval: Time between status checks (seconds)

        Returns:
            bool: True if all expected states were observed
        """
        start_time = time.time()
        current_state_index = 0

        logger.info(f"Monitoring drum {drum_id} for state transitions: {expected_states}")

        while time.time() - start_time < timeout and current_state_index < len(expected_states):
            try:
                status = await self.get_drum_status(drum_id)
                current_state = status.get("state", "unknown")
                expected_state = expected_states[current_state_index]

                if current_state == expected_state:
                    logger.info(f"Drum {drum_id} reached expected state: {expected_state}")
                    current_state_index += 1

                if current_state_index < len(expected_states):
                    await asyncio.sleep(poll_interval)

            except Exception as e:
                logger.warning(f"Error monitoring drum {drum_id}: {e}")
                await asyncio.sleep(poll_interval)

        success = current_state_index >= len(expected_states)

        if success:
            logger.info(f"Drum {drum_id} completed all expected state transitions")
        else:
            logger.error(f"Drum {drum_id} state transition timeout or incomplete")

        return success
