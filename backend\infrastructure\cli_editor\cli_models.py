"""
CLI Models Module
=================

This module defines Pydantic models for representing and validating data structures
used in CLI (Common Layer Interface) file parsing, rendering, and generation within
the RecoaterHMI backend.

Models included:
- Point: Represents a 2D coordinate (x, y).
- Polyline: Represents a sequence of connected points with part and direction metadata.
- Hatch: Represents hatch lines for infill patterns.
- CliLayer: Represents geometry data for a single layer at a specific Z-height.
- ParsedCliFile: Represents the complete parsed CLI file, including headers and layers.

These models ensure type safety, automatic validation, and easy serialization/deserialization
for CLI-related operations. They are used across the cli_editor package for consistent
data handling.

Usage:
    from .cli_models import Point, CliLayer
    point = Point(x=1.0, y=2.0)
    layer = CliLayer(z_height=0.1, polylines=[...])
"""

# Pydantic models are Python classes that inherit from BaseModel. 
# They define data structures using type hints, which Pydantic uses to validate input data at runtime.
# Errors are raised if data does not match the expected types.

from typing import List, Tuple, Optional
from pydantic import BaseModel

class Point(BaseModel):
    """Represents a single 2D point."""
    x: float
    y: float

class Polyline(BaseModel):
    """Represents a polyline with multiple points."""
    part_id: int
    direction: int
    points: List[Point]

class Hatch(BaseModel):
    """Represents a set of hatch lines."""
    group_id: int
    lines: List[Tuple[Point, Point]]

class CliLayer(BaseModel):
    """Represents all geometry data for a single layer at a specific Z-height."""
    z_height: float
    polylines: List[Polyline] = []
    hatches: List[Hatch] = []

class ParsedCliFile(BaseModel):
    """Represents the entire parsed CLI file, including header and layers."""
    header_lines: List[str]
    is_aligned: bool
    layers: List[CliLayer]