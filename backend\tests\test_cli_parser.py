"""
Tests for CLI Parser Service
============================

This module contains comprehensive tests for the CliParserService, including:
- Binary CLI file parsing
- ASCII CLI file parsing (space and slash delimited)
- Inline coordinate format parsing
- Error handling and edge cases
- Layer rendering functionality
- CLI file generation
"""

import pytest
import struct
import io
from typing import List
from unittest.mock import Mock

from backend.infrastructure.cli_editor.editor import (
    Editor, 
    ParsedCliFile, 
    CliLayer, 
    Polyline, 
    Hatch, 
    Point, 
    CliParsingError
)


class TestCliParserService:
    """Test suite for CliParserService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_parser_initialization(self):
        """Test that the parser initializes correctly."""
        assert self.parser is not None
        assert self.parser.logger is not None

    def test_parser_with_custom_logger(self):
        """Test parser initialization with custom logger."""
        mock_logger = Mock()
        parser = Editor(logger=mock_logger)
        assert parser.logger == mock_logger


class TestASCIICliParsing:
    """Test suite for ASCII CLI file parsing."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_space_delimited_basic(self):
        """Test basic space-delimited ASCII CLI parsing."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 3
10.0 5.0
15.0 5.0
10.0 10.0
$$LAYER 16.0
$$HATCHES 1 1
0.0 0.0 5.0 5.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 2
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert len(result.layers[0].polylines) == 1
        assert len(result.layers[1].hatches) == 1
        
        # Check polyline details
        polyline = result.layers[0].polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 3
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0

    def test_slash_delimited_basic(self):
        """Test basic slash-delimited ASCII CLI parsing."""
        cli_content = """$$HEADERSTART
$$ASCII
$$UNITS/00000000.005000
$$VERSION/200
$$LABEL/1,default
$$DATE/060623
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$LAYER/16.0
$$LAYER/32.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 3
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert result.layers[2].z_height == 32.0
        assert len(result.header_lines) == 7  # Updated expected count

    def test_inline_polyline_coordinates(self):
        """Test parsing polylines with inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER/0.0
$$POLYLINE/1,1,4,10.0,5.0,15.0,5.0,15.0,10.0,10.0,10.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert len(result.layers) == 1
        layer = result.layers[0]
        assert len(layer.polylines) == 1
        
        polyline = layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 1
        assert len(polyline.points) == 4
        
        expected_points = [(10.0, 5.0), (15.0, 5.0), (15.0, 10.0), (10.0, 10.0)]
        for i, (exp_x, exp_y) in enumerate(expected_points):
            assert polyline.points[i].x == exp_x
            assert polyline.points[i].y == exp_y

    def test_inline_hatch_coordinates(self):
        """Test parsing hatches with inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER/0.0
$$HATCHES/1,2,0.0,0.0,5.0,5.0,5.0,0.0,0.0,5.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert len(result.layers) == 1
        layer = result.layers[0]
        assert len(layer.hatches) == 1
        
        hatch = layer.hatches[0]
        assert hatch.group_id == 1
        assert len(hatch.lines) == 2
        
        # Check first hatch line
        line1 = hatch.lines[0]
        assert line1[0].x == 0.0 and line1[0].y == 0.0
        assert line1[1].x == 5.0 and line1[1].y == 5.0
        
        # Check second hatch line
        line2 = hatch.lines[1]
        assert line2[0].x == 5.0 and line2[0].y == 0.0
        assert line2[1].x == 0.0 and line2[1].y == 5.0

    def test_large_polyline_inline(self):
        """Test parsing a large polyline with many inline coordinates."""
        # Create a polyline with 50 points
        coords = []
        expected_points = []
        for i in range(50):
            x, y = i * 10.0, i * 5.0
            coords.extend([str(x), str(y)])
            expected_points.append((x, y))
        
        coord_str = ",".join(coords)
        cli_content = f"""$$HEADEREND
$$LAYER/0.0
$$POLYLINE/1,1,50,{coord_str}"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        layer = result.layers[0]
        polyline = layer.polylines[0]
        
        assert len(polyline.points) == 50
        for i, (exp_x, exp_y) in enumerate(expected_points):
            assert polyline.points[i].x == exp_x
            assert polyline.points[i].y == exp_y

    def test_mixed_coordinate_separators(self):
        """Test parsing with mixed comma and space separators."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 2
10.0,5.0
15.0 10.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        polyline = result.layers[0].polylines[0]
        assert len(polyline.points) == 2
        assert polyline.points[0].x == 10.0
        assert polyline.points[0].y == 5.0
        assert polyline.points[1].x == 15.0
        assert polyline.points[1].y == 10.0

    def test_geometry_start_marker(self):
        """Test that $$GEOMETRYSTART marker is properly handled."""
        cli_content = """$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert len(result.layers) == 1
        assert result.layers[0].z_height == 0.0

    def test_alignment_detection(self):
        """Test detection of alignment in header."""
        cli_content = """$$ALIGN
$$HEADEREND
$$LAYER 0.0"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert result.is_aligned is True

    def test_empty_lines_ignored(self):
        """Test that empty lines are properly ignored."""
        cli_content = """$$HEADEREND

$$LAYER 0.0

$$POLYLINE 1 0 1

10.0 5.0

"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert len(result.layers) == 1
        assert len(result.layers[0].polylines) == 1
        assert len(result.layers[0].polylines[0].points) == 1


class TestASCIICliParsingErrors:
    """Test suite for ASCII CLI parsing error cases."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_missing_layer_height(self):
        """Test error handling for missing layer height."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$LAYER"""

        # Add a valid layer first so it's detected as ASCII, then the invalid one
        with pytest.raises(CliParsingError, match="missing Z height"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_polyline_before_layer(self):
        """Test error handling for polyline before layer."""
        cli_content = """$$HEADEREND
$$POLYLINE 1 0 1"""

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_polyline_args(self):
        """Test error handling for insufficient polyline arguments."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0"""

        with pytest.raises(CliParsingError, match="expected 3 args"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_invalid_polyline_parameters(self):
        """Test error handling for invalid polyline parameters."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE abc def ghi"""

        with pytest.raises(CliParsingError, match="Invalid \\$\\$POLYLINE parameters"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_polyline_points(self):
        """Test error handling for insufficient polyline points."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 3
10.0 5.0
15.0 10.0"""

        with pytest.raises(CliParsingError, match="Expected 3 points, but only found 2"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_invalid_coordinate_format(self):
        """Test error handling for invalid coordinate format."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE 1 0 1
invalid_coord"""

        with pytest.raises(CliParsingError, match="expected X Y coordinates"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_insufficient_inline_coordinates(self):
        """Test error handling for insufficient inline coordinates."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$POLYLINE/1,1,3,10.0,5.0,15.0"""

        with pytest.raises(CliParsingError, match="Insufficient coordinates in polyline"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_hatches_before_layer(self):
        """Test error handling for hatches before layer."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$HATCHES 1 1"""

        # Add a layer first to trigger ASCII detection, then move hatches before it will cause error during parsing
        cli_content = """$$HEADEREND
$$HATCHES 1 1
$$LAYER 0.0"""

        with pytest.raises(CliParsingError, match="before any \\$\\$LAYER"):
            self.parser.parse(cli_content.encode('ascii'))


class TestBinaryCliParsing:
    """Test suite for binary CLI file parsing."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def create_binary_cli(self, layers_data: List[dict], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """Helper method to create binary CLI data for testing."""
        stream = io.BytesIO()
        
        # Write header
        if header_lines is None:
            header_lines = ["$$HEADEREND"]
        
        header_str = "\n".join(header_lines)
        if not header_str.endswith("$$HEADEREND"):
            header_str += "\n$$HEADEREND"
        
        stream.write(header_str.encode('ascii'))
        
        # Write geometry data
        for layer_data in layers_data:
            # Layer command (127)
            stream.write(struct.pack("<H", 127))
            if is_aligned:
                stream.write(b'\x00\x00')
            stream.write(struct.pack("<f", layer_data['z_height']))
            
            # Polylines (130)
            for poly_data in layer_data.get('polylines', []):
                stream.write(struct.pack("<H", 130))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<iii", poly_data['part_id'], poly_data['direction'], len(poly_data['points'])))
                for point in poly_data['points']:
                    stream.write(struct.pack("<ff", point[0], point[1]))
            
            # Hatches (132)
            for hatch_data in layer_data.get('hatches', []):
                stream.write(struct.pack("<H", 132))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<ii", hatch_data['group_id'], len(hatch_data['lines'])))
                for line in hatch_data['lines']:
                    stream.write(struct.pack("<ffff", line[0][0], line[0][1], line[1][0], line[1][1]))
        
        return stream.getvalue()

    def test_binary_basic_parsing(self):
        """Test basic binary CLI parsing."""
        layers_data = [
            {
                'z_height': 0.0,
                'polylines': [
                    {
                        'part_id': 1,
                        'direction': 0,
                        'points': [(10.0, 5.0), (15.0, 5.0), (15.0, 10.0)]
                    }
                ],
                'hatches': [
                    {
                        'group_id': 1,
                        'lines': [((0.0, 0.0), (5.0, 5.0))]
                    }
                ]
            }
        ]
        
        binary_data = self.create_binary_cli(layers_data)
        result = self.parser.parse(binary_data)
        
        assert len(result.layers) == 1
        layer = result.layers[0]
        assert layer.z_height == 0.0
        assert len(layer.polylines) == 1
        assert len(layer.hatches) == 1
        
        polyline = layer.polylines[0]
        assert polyline.part_id == 1
        assert polyline.direction == 0
        assert len(polyline.points) == 3

    def test_binary_aligned_format(self):
        """Test binary CLI parsing with alignment."""
        layers_data = [{'z_height': 0.0}]
        header_lines = ["$$ALIGN", "$$HEADEREND"]
        
        binary_data = self.create_binary_cli(layers_data, header_lines, is_aligned=True)
        result = self.parser.parse(binary_data)
        
        assert result.is_aligned is True
        assert len(result.layers) == 1

    def test_binary_multiple_layers(self):
        """Test binary CLI with multiple layers."""
        layers_data = [
            {'z_height': 0.0},
            {'z_height': 16.0},
            {'z_height': 32.0}
        ]
        
        binary_data = self.create_binary_cli(layers_data)
        result = self.parser.parse(binary_data)
        
        assert len(result.layers) == 3
        assert result.layers[0].z_height == 0.0
        assert result.layers[1].z_height == 16.0
        assert result.layers[2].z_height == 32.0


class TestCliLayerRendering:
    """Test suite for CLI layer rendering functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_render_empty_layer(self):
        """Test rendering an empty layer."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        
        png_bytes = self.parser.render_layer_to_png(layer)
        
        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0
        # PNG files start with specific bytes
        assert png_bytes.startswith(b'\x89PNG\r\n\x1a\n')

    def test_render_layer_with_geometry(self):
        """Test rendering a layer with polylines and hatches."""
        polyline = Polyline(
            part_id=1,
            direction=0,
            points=[Point(x=0, y=0), Point(x=10, y=10), Point(x=20, y=0)]
        )
        hatch = Hatch(
            group_id=1,
            lines=[(Point(x=5, y=5), Point(x=15, y=15))]
        )
        layer = CliLayer(z_height=0.0, polylines=[polyline], hatches=[hatch])
        
        png_bytes = self.parser.render_layer_to_png(layer, width=400, height=300)
        
        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0

    def test_render_custom_size(self):
        """Test rendering with custom image dimensions."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        
        png_bytes = self.parser.render_layer_to_png(layer, width=1024, height=768)
        
        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0


class TestCliGeneration:
    """Test suite for CLI file generation functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_generate_single_layer_cli(self):
        """Test generating CLI data for a single layer."""
        polyline = Polyline(
            part_id=1,
            direction=0,
            points=[Point(x=0, y=0), Point(x=10, y=10)]
        )
        layer = CliLayer(z_height=16.0, polylines=[polyline], hatches=[])
        
        cli_bytes = self.parser.generate_single_layer_cli(layer)
        
        assert isinstance(cli_bytes, bytes)
        assert len(cli_bytes) > 0
        
        # Parse the generated data to verify it's valid
        parsed = self.parser.parse(cli_bytes)
        assert len(parsed.layers) == 1
        assert parsed.layers[0].z_height == 16.0
        assert len(parsed.layers[0].polylines) == 1

    def test_generate_multi_layer_cli(self):
        """Test generating CLI data for multiple layers."""
        layers = [
            CliLayer(z_height=0.0, polylines=[], hatches=[]),
            CliLayer(z_height=16.0, polylines=[], hatches=[]),
            CliLayer(z_height=32.0, polylines=[], hatches=[])
        ]
        
        cli_bytes = self.parser.generate_cli_from_layer_range(layers)
        
        assert isinstance(cli_bytes, bytes)
        assert len(cli_bytes) > 0
        
        # Parse the generated data to verify it's valid
        parsed = self.parser.parse(cli_bytes)
        assert len(parsed.layers) == 3
        assert parsed.layers[0].z_height == 0.0
        assert parsed.layers[1].z_height == 16.0
        assert parsed.layers[2].z_height == 32.0

    def test_generate_with_custom_header(self):
        """Test generating CLI data with custom header."""
        layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        header_lines = ["$$BINARY", "$$VERSION 200", "$$HEADEREND"]
        
        cli_bytes = self.parser.generate_single_layer_cli(layer, header_lines=header_lines)
        
        parsed = self.parser.parse(cli_bytes)
        assert "$$VERSION 200" in parsed.header_lines

    def test_generate_empty_layer_range_error(self):
        """Test error handling for empty layer range."""
        with pytest.raises(CliParsingError, match="empty layer range"):
            self.parser.generate_cli_from_layer_range([])


class TestFormatDetection:
    """Test suite for format detection functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_ascii_space_detection(self):
        """Test detection of space-delimited ASCII format."""
        cli_content = """$$HEADEREND
$$LAYER 0.0"""
        
        result = self.parser.parse(cli_content.encode('ascii'))
        
        # Should be parsed as ASCII, not binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1

    def test_ascii_slash_detection(self):
        """Test detection of slash-delimited ASCII format."""
        cli_content = """$$HEADEREND
$$LAYER/0.0"""
        
        result = self.parser.parse(cli_content.encode('ascii'))
        
        # Should be parsed as ASCII, not binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1

    def test_binary_detection(self):
        """Test detection of binary format."""
        # Create a minimal binary CLI file
        binary_data = b"$$HEADEREND" + struct.pack("<H", 127) + struct.pack("<f", 0.0)
        
        result = self.parser.parse(binary_data)
        
        # Should be parsed as binary
        assert isinstance(result, ParsedCliFile)
        assert len(result.layers) == 1

    def test_looks_like_ascii_method(self):
        """Test the _looks_like_ascii helper method."""
        ascii_data = b"$$HEADEREND\n$$LAYER 0.0"
        binary_data = b"$$HEADEREND" + b"\x7f\x00\x00\x00\x00\x00"
        
        assert self.parser._looks_like_ascii(ascii_data) is True
        assert self.parser._looks_like_ascii(binary_data) is False


class TestEdgeCases:
    """Test suite for edge cases and unusual scenarios."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = Editor()

    def test_unrecognized_commands(self):
        """Test handling of unrecognized commands."""
        cli_content = """$$HEADEREND
$$LAYER 0.0
$$UNKNOWN_COMMAND some data
$$POLYLINE 1 0 1
10.0 5.0"""

        # Should not raise an error, just log a warning
        result = self.parser.parse(cli_content.encode('ascii'))
        
        assert len(result.layers) == 1
        assert len(result.layers[0].polylines) == 1

    def test_malformed_header(self):
        """Test handling of files without proper header end."""
        # This test should expect the CliParsingError for missing $$HEADEREND
        cli_content = """$$SOMEHEADER
$$LAYER 0.0"""

        with pytest.raises(CliParsingError, match="EOF reached before"):
            self.parser.parse(cli_content.encode('ascii'))

    def test_zero_dimension_geometry(self):
        """Test rendering of zero-dimension geometry."""
        # Single point polyline (same point repeated)
        polyline = Polyline(
            part_id=1,
            direction=0,
            points=[Point(x=10, y=10), Point(x=10, y=10)]
        )
        layer = CliLayer(z_height=0.0, polylines=[polyline], hatches=[])
        
        png_bytes = self.parser.render_layer_to_png(layer)
        
        assert isinstance(png_bytes, bytes)
        assert len(png_bytes) > 0

    def test_large_coordinate_values(self):
        """Test handling of very large coordinate values."""
        large_value = 1e6
        cli_content = f"""$$HEADEREND
$$LAYER/0.0
$$POLYLINE/1,1,2,{large_value},{large_value},{-large_value},{-large_value}"""

        result = self.parser.parse(cli_content.encode('ascii'))
        
        polyline = result.layers[0].polylines[0]
        assert polyline.points[0].x == large_value
        assert polyline.points[0].y == large_value
        assert polyline.points[1].x == -large_value
        assert polyline.points[1].y == -large_value
