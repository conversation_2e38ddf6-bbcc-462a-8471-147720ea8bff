"""
Tests for Multi-Material Job Manager
====================================

Test suite for the multi-material job management functionality
including job creation, CLI file processing, and drum coordination.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict

from app.services.multilayer_job_manager import MultiMaterialJobManager, MultiMaterialJobError
from app.models.multilayer_job import MultiMaterialJobState, JobStatus
from backend.infrastructure.cli_editor.editor import ParsedCliFile, CliLayer, Point, Polyline


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client for testing."""
    client = Mock()
    client.get_state = Mock(return_value={"state": "ready"})
    client._make_request = Mock()
    return client


@pytest.fixture
def job_manager(mock_recoater_client):
    """Create a job manager instance for testing."""
    return MultiMaterialJobManager(mock_recoater_client)


@pytest.fixture
def sample_cli_files():
    """Create sample CLI files for testing."""
    # Create sample polylines and layers
    points = [Point(x=0.0, y=0.0), Point(x=10.0, y=10.0)]
    polyline = Polyline(part_id=1, direction=0, points=points)
    
    # Create layers for each drum
    layers_drum_0 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline]),
        CliLayer(z_height=0.3, polylines=[polyline])
    ]

    layers_drum_1 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline])
    ]

    layers_drum_2 = [
        CliLayer(z_height=0.1, polylines=[polyline]),
        CliLayer(z_height=0.2, polylines=[polyline]),
        CliLayer(z_height=0.3, polylines=[polyline]),
        CliLayer(z_height=0.4, polylines=[polyline])
    ]
    
    # Create parsed CLI files
    cli_files = {
        0: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_0,
            is_aligned=False
        ),
        1: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_1,
            is_aligned=False
        ),
        2: ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$UNITS/MM", "$$HEADEREND"],
            layers=layers_drum_2,
            is_aligned=False
        )
    }
    
    return cli_files


class TestMultiMaterialJobManager:
    """Test cases for MultiMaterialJobManager."""
    
    def test_initialization(self, job_manager):
        """Test job manager initialization."""
        assert job_manager.current_job is None
        assert len(job_manager.cli_cache) == 0
    
    def test_add_cli_file(self, job_manager, sample_cli_files):
        """Test adding CLI files to cache."""
        file_id = "test-file-1"
        job_manager.add_cli_file(file_id, sample_cli_files[0])
        
        assert file_id in job_manager.cli_cache
        assert job_manager.cli_cache[file_id] == sample_cli_files[0]
    
    @pytest.mark.asyncio
    async def test_create_job_success(self, job_manager, sample_cli_files):
        """Test successful job creation with 3 CLI files."""
        # Add CLI files to cache
        file_ids = {}
        for drum_id, cli_file in sample_cli_files.items():
            file_id = f"file-drum-{drum_id}"
            job_manager.add_cli_file(file_id, cli_file)
            file_ids[drum_id] = file_id
        
        # Create job
        job_state = await job_manager.create_job(file_ids)
        
        # Verify job creation
        assert job_state is not None
        assert job_state.file_ids == file_ids
        assert job_state.total_layers == 4  # Maximum of 3, 2, 4 layers
        assert len(job_state.drums) == 3
        
        # Verify drum states
        for drum_id in [0, 1, 2]:
            drum = job_state.drums[drum_id]
            assert drum.drum_id == drum_id
            assert drum.file_id == file_ids[drum_id]
            assert drum.status == "ready"
    
    @pytest.mark.asyncio
    async def test_create_job_invalid_file_count(self, job_manager):
        """Test job creation with invalid number of files."""
        # Test with too many files (should fail)
        file_ids = {0: "file-1", 1: "file-2", 2: "file-3", 3: "file-4"}

        with pytest.raises(MultiMaterialJobError, match="Must provide 1-3 CLI files"):
            await job_manager.create_job(file_ids)
    
    @pytest.mark.asyncio
    async def test_create_job_dual_material_valid(self, job_manager, sample_cli_files):
        """Test job creation with valid dual material setup (2 files)."""
        # Add sample files to cache with string keys
        job_manager.cli_cache["file-1"] = sample_cli_files[0]
        job_manager.cli_cache["file-2"] = sample_cli_files[0]  # Use same structure for both

        # Test dual material job (2 files)
        file_ids = {0: "file-1", 1: "file-2"}

        # Mock the empty layer template loading
        with patch.object(job_manager, '_load_empty_layer_template', return_value="empty-template-id"):
            # Mock empty template in cache
            job_manager.cli_cache["empty-template-id"] = sample_cli_files[0]  # Use same structure

            job_state = await job_manager.create_job(file_ids)

            # Should have 3 drums (2 provided + 1 empty template)
            assert len(job_state.file_ids) == 3
            assert job_state.file_ids[0] == "file-1"
            assert job_state.file_ids[1] == "file-2"
            assert job_state.file_ids[2] == "empty-template-id"  # Auto-added empty template

    @pytest.mark.asyncio
    async def test_create_job_missing_file(self, job_manager, sample_cli_files):
        """Test job creation with missing CLI file in cache."""
        file_ids = {0: "missing-file", 1: "file-2", 2: "file-3"}
        with pytest.raises(MultiMaterialJobError, match="CLI file missing-file not found in cache"):
            await job_manager.create_job(file_ids)
    
    @pytest.mark.asyncio
    async def test_start_job_success(self, job_manager, sample_cli_files):
        """Test successful job start."""
        # Setup job
        file_ids = {}
        for drum_id, cli_file in sample_cli_files.items():
            file_id = f"file-drum-{drum_id}"
            job_manager.add_cli_file(file_id, cli_file)
            file_ids[drum_id] = file_id
        
        await job_manager.create_job(file_ids)
        
        # Start job
        result = await job_manager.start_job()
        
        assert result is True
        assert job_manager.current_job.is_active is True
        assert job_manager.current_job.status == JobStatus.RUNNING
        assert job_manager.current_job.current_layer == 1
    
    @pytest.mark.asyncio
    async def test_start_job_no_active_job(self, job_manager):
        """Test starting job when no job is active."""
        with pytest.raises(MultiMaterialJobError, match="No active job to start"):
            await job_manager.start_job()
    
    @pytest.mark.asyncio
    async def test_cancel_job(self, job_manager, sample_cli_files):
        """Test job cancellation."""
        # Setup and start job
        file_ids = {}
        for drum_id, cli_file in sample_cli_files.items():
            file_id = f"file-drum-{drum_id}"
            job_manager.add_cli_file(file_id, cli_file)
            file_ids[drum_id] = file_id
        
        await job_manager.create_job(file_ids)
        await job_manager.start_job()
        
        # Cancel job
        result = await job_manager.cancel_job()
        
        assert result is True
        assert job_manager.current_job.is_active is False
        assert job_manager.current_job.status == JobStatus.CANCELLED
    
    def test_get_job_status_no_job(self, job_manager):
        """Test getting status when no job is active."""
        status = job_manager.get_job_status()
        assert status is None
    
    def test_get_job_status_with_job(self, job_manager, sample_cli_files):
        """Test getting job status with active job."""
        # Setup job
        file_ids = {}
        for drum_id, cli_file in sample_cli_files.items():
            file_id = f"file-drum-{drum_id}"
            job_manager.add_cli_file(file_id, cli_file)
            file_ids[drum_id] = file_id
        
        # Create job synchronously for this test
        job_state = MultiMaterialJobState(file_ids=file_ids, total_layers=4)
        job_manager.current_job = job_state
        
        status = job_manager.get_job_status()
        
        assert status is not None
        assert status["job_id"] == job_state.job_id
        assert status["total_layers"] == 4
        assert status["current_layer"] == 0
        assert "drums" in status
        assert len(status["drums"]) == 3
    
    def test_get_drum_status(self, job_manager, sample_cli_files):
        """Test getting individual drum status."""
        # Setup job
        file_ids = {}
        for drum_id, cli_file in sample_cli_files.items():
            file_id = f"file-drum-{drum_id}"
            job_manager.add_cli_file(file_id, cli_file)
            file_ids[drum_id] = file_id
        
        job_state = MultiMaterialJobState(file_ids=file_ids, total_layers=4)
        job_manager.current_job = job_state
        
        # Test valid drum
        drum_status = job_manager.get_drum_status(0)
        assert drum_status is not None
        assert drum_status["drum_id"] == 0
        
        # Test invalid drum
        drum_status = job_manager.get_drum_status(5)
        assert drum_status is None
    
    def test_generate_single_layer_cli(self, job_manager, sample_cli_files):
        """Test single layer CLI generation."""
        cli_file = sample_cli_files[0]
        
        # Test valid layer
        cli_data = job_manager._generate_single_layer_cli(cli_file, 0)
        assert isinstance(cli_data, bytes)
        assert b"$$LAYER" in cli_data
        assert b"$$POLYLINE" in cli_data
        
        # Test invalid layer index
        cli_data = job_manager._generate_single_layer_cli(cli_file, 10)
        assert cli_data == b""


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
